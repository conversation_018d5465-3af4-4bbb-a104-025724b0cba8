<template>
  <span>
    <template v-for="(item, index) in options">
      <template v-if="values.includes(item[valueName])">
        <el-tag
          :disable-transitions="true"
          :key="item[valueName]"
          :index="index"
          v-bind="getStyle(item)"
          size="mini"
          effect="plain"
        >
          <span
            class="el-icon-loading"
            v-show="checkLoading(item)"
            style="font-size: 14px"
          ></span>
          {{ item[labelName] }}
        </el-tag>
      </template>
    </template>
  </span>
</template>

<script>
export default {
  name: "DictTag",
  props: {
    options: {
      type: Array,
      default: null,
    },
    valueName: {
      type: String,
      default: "value",
    },
    labelName: {
      type: String,
      default: "label",
    },
    value: [Number, String, Array],
    styles: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  computed: {
    values() {
      if (this.value !== null && typeof this.value !== "undefined") {
        return Array.isArray(this.value) ? this.value : [String(this.value)];
      } else {
        return [];
      }
    },
  },
  methods: {
    getStyle(item) {
      if (item.listClass) {
        return { type: item.listClass, class: item.cssClass || "" };
      } else {
        return {
          type: this.styles[item[this.valueName]]?.listClass || "",
          class: "",
        };
      }
    },
    checkLoading(item) {
      if (item.loading == true) {
        return true;
      }
      if (this.styles[item[this.valueName]]?.loading == true) {
        return true;
      }
      return false;
    },
  },
};
</script>
<style scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}
</style>
