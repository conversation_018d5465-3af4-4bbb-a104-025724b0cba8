<template>
  <div class="full-height" style="display: flex; flex-direction: column">
    <div class="comm-page-title">
      <page-header>
        <div class="app-header" style="display: flex">
          <div class="app-icon">
            <div
              class="emoji-icon"
              :style="{ background: appInfo.icon_background }"
              v-if="appInfo.icon_type == 'emoji'"
            >
              <emoji
                :data="emojiData"
                :emoji="appInfo.icon"
                :native="true"
                :size="20"
              />
              <!-- {{ appInfo.icon }} -->
            </div>
            <img v-else :src="appInfo.icon_url" object-fit="contain" />
          </div>
          <div class="app-right">
            <div class="app-title" style="font-weight: bold">
              {{ appInfo.name }}
            </div>
          </div>
        </div>
      </page-header>
    </div>
    <i-frame v-if="url" :src="url" style="flex: 1" id="bdIframe" />
  </div>
</template>
<script>
import iFrame from "@/components/iFrame/index";
import { getAppDifyUrl, getAppDetail } from "@/api/ai/app";
import { getPortalInfo } from "@/api/ai/portal";
import data from "emoji-mart-vue-fast/data/all.json";
import { Emoji, EmojiIndex } from "emoji-mart-vue-fast";
const emojiData = new EmojiIndex(data);
export default {
  name: "IframeCom",
  components: { iFrame, Emoji },
  data() {
    return {
      emojiData,
      url: "",
      isFixed: false,
      appInfo: {},
      appId: "",
      timer: null,
      newSessionId: "",
    };
  },
  created() {
    this.refreshData();
  },
  methods: {
    refreshData() {
      let appId = this.$route.query.appId;
      if (appId) {
        this.appId = appId;
        this.getAppInfo();
        this.getAppUrl();
      } else {
        this.getDefaultApp();
      }
    },
    getDefaultApp() {
      getPortalInfo({}).then((res) => {
        let appId = res.data.defaultAppId;
        this.appId = appId;
        this.getAppInfo();
        this.getAppUrl();
      });
    },
    getAppUrl() {
      getAppDifyUrl({ appId: this.appId }).then((res) => {
        let conversationIdInfo =
          JSON.parse(localStorage.getItem("conversationIdInfo")) || {};
        delete conversationIdInfo[this.appId];
        localStorage.setItem(
          "conversationIdInfo",
          JSON.stringify(conversationIdInfo),
        );
        this.url = res.data;
        this.watchNewSession();
      });
    },
    getAppInfo() {
      getAppDetail({ appId: this.appId }).then((res) => {
        this.appInfo = res.data;
      });
    },
    watchNewSession() {
      // 监听localstorage，会话id产生变化，触发会话列表刷新
      let conversationIdInfo =
        JSON.parse(localStorage.getItem("conversationIdInfo")) || {};
      if (!!conversationIdInfo[this.appId]) {
        let newSessionId =
          Object.values(conversationIdInfo[this.appId])[0] || "";
        if (newSessionId && newSessionId != this.newSessionId) {
          this.newSessionId = newSessionId;
          window.$eventBus.$emit("refreshSessionList");
        }
      }
      this.timer = setTimeout(() => {
        this.watchNewSession();
      }, 500);
    },
  },
  beforeDestroy() {
    clearTimeout(this.timer);
  },
  watch: {
    $route(val) {
      this.refreshData();
    },
  },
};
</script>
<style lang="scss" scoped>
.app-header {
  display: flex;
  line-height: 32px;
  .app-icon {
    width: 32px;
    margin-right: 10px;
    text-align: center;
    .emoji-icon {
      border-radius: 4px;
    }
    img {
      width: 100%;
      border-radius: 4px;
    }
  }
}
</style>
