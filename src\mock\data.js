export const btnsPerssion = [
  {
    id: "***********62706333",
    menuCode: "PLAT_SYSMG_ROLE_QUERY",
    menuName: "查询",
    menuCss: "",
    menuType: 1,
    menuPid: "***********62706329",
    menuUrl: "",
    menuActive: "",
    menuInitCert: "",
    menuNameEn: "QUERY",
    menuAlwaysShow: 0,
    menuComp: "iframeCom/index",
    menuHidden: 0,
    menuCache: 1,
    menuLink: "",
    menuTitle: "",
    menuQuery: "",
    menuMark: "role:query",
    serverCode: "SecPlatWeb",
    menuSort: 0,
    createBy: "",
    createDate: "2023-09-04 16:56:39",
    updateBy: "",
    updateDate: "2023-09-04 16:56:39",
    flagDel: 0,
    remark: "",
  },
  {
    id: "***********62706332",
    menuCode: "PLAT_SYSMG_ROLE_DEL",
    menuName: "删除",
    menuCss: "",
    menuType: 1,
    menuPid: "***********62706329",
    menuUrl: "",
    menuActive: "",
    menuInitCert: "",
    menuNameEn: "DELETE",
    menuAlwaysShow: 0,
    menuComp: "iframeCom/index",
    menuHidden: 0,
    menuCache: 1,
    menuLink: "",
    menuTitle: "",
    menuQuery: "",
    menuMark: "role:delete",
    serverCode: "SecPlatWeb",
    menuSort: 0,
    createBy: "",
    createDate: "2023-09-04 16:56:39",
    updateBy: "",
    updateDate: "2023-09-04 16:56:39",
    flagDel: 0,
    remark: "",
  },
  {
    id: "***********62706331",
    menuCode: "PLAT_SYSMG_ROLE_EDIT",
    menuName: "修改",
    menuCss: "",
    menuType: 1,
    menuPid: "***********62706329",
    menuUrl: "",
    menuActive: "",
    menuInitCert: "",
    menuNameEn: "UPDATE",
    menuAlwaysShow: 0,
    menuComp: "iframeCom/index",
    menuHidden: 0,
    menuCache: 1,
    menuLink: "",
    menuTitle: "",
    menuQuery: "",
    menuMark: "role:update",
    serverCode: "SecPlatWeb",
    menuSort: 0,
    createBy: "",
    createDate: "2023-09-04 16:56:39",
    updateBy: "",
    updateDate: "2023-09-04 16:56:39",
    flagDel: 0,
    remark: "",
  },
  {
    id: "***********62706330",
    menuCode: "PLAT_SYSMG_ROLE_ADD",
    menuName: "新增",
    menuCss: "",
    menuType: 1,
    menuPid: "***********62706329",
    menuUrl: "",
    menuActive: "",
    menuInitCert: "",
    menuNameEn: "ADD",
    menuAlwaysShow: 0,
    menuComp: "iframeCom/index",
    menuHidden: 0,
    menuCache: 1,
    menuLink: "",
    menuTitle: "",
    menuQuery: "",
    menuMark: "role:add",
    serverCode: "SecPlatWeb",
    menuSort: 0,
    createBy: "",
    createDate: "2023-09-04 16:56:39",
    updateBy: "",
    updateDate: "2023-09-04 16:56:39",
    flagDel: 0,
    remark: "",
  },
  {
    id: "***********62706328",
    menuCode: "PLAT_SYSMG_USER_QUERY",
    menuName: "查询",
    menuCss: "",
    menuType: 1,
    menuPid: "***********62706324",
    menuUrl: "",
    menuActive: "",
    menuInitCert: "",
    menuNameEn: "QUERY",
    menuAlwaysShow: 0,
    menuComp: "iframeCom/index",
    menuHidden: 0,
    menuCache: 1,
    menuLink: "",
    menuTitle: "",
    menuQuery: "",
    menuMark: "user:query",
    serverCode: "SecPlatWeb",
    menuSort: 0,
    createBy: "",
    createDate: "2023-09-04 16:56:39",
    updateBy: "",
    updateDate: "2023-09-04 16:56:39",
    flagDel: 0,
    remark: "",
  },
  {
    id: "***********62706327",
    menuCode: "PLAT_SYSMG_USER_DEL",
    menuName: "删除",
    menuCss: "",
    menuType: 1,
    menuPid: "***********62706324",
    menuUrl: "",
    menuActive: "",
    menuInitCert: "",
    menuNameEn: "DELETE",
    menuAlwaysShow: 0,
    menuComp: "iframeCom/index",
    menuHidden: 0,
    menuCache: 1,
    menuLink: "",
    menuTitle: "",
    menuQuery: "",
    menuMark: "user:delete",
    serverCode: "SecPlatWeb",
    menuSort: 0,
    createBy: "",
    createDate: "2023-09-04 16:56:39",
    updateBy: "",
    updateDate: "2023-09-04 16:56:39",
    flagDel: 0,
    remark: "",
  },
  {
    id: "***********62706326",
    menuCode: "PLAT_SYSMG_USER_EDIT",
    menuName: "修改",
    menuCss: "",
    menuType: 1,
    menuPid: "***********62706324",
    menuUrl: "",
    menuActive: "",
    menuInitCert: "",
    menuNameEn: "UPDATE",
    menuAlwaysShow: 0,
    menuComp: "iframeCom/index",
    menuHidden: 0,
    menuCache: 1,
    menuLink: "",
    menuTitle: "",
    menuQuery: "",
    menuMark: "user:update",
    serverCode: "SecPlatWeb",
    menuSort: 0,
    createBy: "",
    createDate: "2023-09-04 16:56:39",
    updateBy: "",
    updateDate: "2023-09-04 16:56:39",
    flagDel: 0,
    remark: "",
  },
  {
    id: "***********62706325",
    menuCode: "PLAT_SYSMG_USER_ADD",
    menuName: "新增",
    menuCss: "",
    menuType: 1,
    menuPid: "***********62706324",
    menuUrl: "",
    menuActive: "",
    menuInitCert: "",
    menuNameEn: "ADD",
    menuAlwaysShow: 0,
    menuComp: "iframeCom/index",
    menuHidden: 0,
    menuCache: 1,
    menuLink: "",
    menuTitle: "",
    menuQuery: "",
    menuMark: "user:add",
    serverCode: "SecPlatWeb",
    menuSort: 0,
    createBy: "",
    createDate: "2023-09-04 16:56:39",
    updateBy: "",
    updateDate: "2023-09-04 16:56:39",
    flagDel: 0,
    remark: "",
  },
  {
    id: "***********62706226",
    menuCode: "PLAT_TENANT_LIST_INIT",
    menuName: "人员信息",
    menuCss: "",
    menuType: 1,
    menuPid: "***********62706221",
    menuUrl: "",
    menuActive: "",
    menuInitCert: "",
    menuNameEn: "PERSON VIEW",
    menuAlwaysShow: 0,
    menuComp: "iframeCom/index",
    menuHidden: 0,
    menuCache: 1,
    menuLink: "",
    menuTitle: "",
    menuQuery: "",
    menuMark: "tenant:viewPerson",
    serverCode: "SecPlatWeb",
    menuSort: 0,
    createBy: "",
    createDate: "2023-09-04 16:56:39",
    updateBy: "",
    updateDate: "2023-09-04 16:56:39",
    flagDel: 0,
    remark: "",
  },
  {
    id: "***********62706225",
    menuCode: "PLAT_TENANT_LIST_INIT",
    menuName: "初始化",
    menuCss: "",
    menuType: 1,
    menuPid: "***********62706221",
    menuUrl: "",
    menuActive: "",
    menuInitCert: "",
    menuNameEn: "INIT",
    menuAlwaysShow: 0,
    menuComp: "iframeCom/index",
    menuHidden: 0,
    menuCache: 1,
    menuLink: "",
    menuTitle: "",
    menuQuery: "",
    menuMark: "tenant:init",
    serverCode: "SecPlatWeb",
    menuSort: 0,
    createBy: "",
    createDate: "2023-09-04 16:56:39",
    updateBy: "",
    updateDate: "2023-09-04 16:56:39",
    flagDel: 0,
    remark: "",
  },
  {
    id: "***********62706224",
    menuCode: "PLAT_TENANT_LIST_DELETE",
    menuName: "删除",
    menuCss: "",
    menuType: 1,
    menuPid: "***********62706221",
    menuUrl: "",
    menuActive: "",
    menuInitCert: "",
    menuNameEn: "DELETE",
    menuAlwaysShow: 0,
    menuComp: "iframeCom/index",
    menuHidden: 0,
    menuCache: 1,
    menuLink: "",
    menuTitle: "",
    menuQuery: "",
    menuMark: "tenant:delete",
    serverCode: "SecPlatWeb",
    menuSort: 0,
    createBy: "",
    createDate: "2023-09-04 16:56:39",
    updateBy: "",
    updateDate: "2023-09-04 16:56:39",
    flagDel: 0,
    remark: "",
  },
  {
    id: "***********62706223",
    menuCode: "PLAT_TENANT_LIST_EDIT",
    menuName: "编辑",
    menuCss: "",
    menuType: 1,
    menuPid: "***********62706221",
    menuUrl: "",
    menuActive: "",
    menuInitCert: "",
    menuNameEn: "EDIT",
    menuAlwaysShow: 0,
    menuComp: "iframeCom/index",
    menuHidden: 0,
    menuCache: 1,
    menuLink: "",
    menuTitle: "",
    menuQuery: "",
    menuMark: "tenant:edit",
    serverCode: "SecPlatWeb",
    menuSort: 0,
    createBy: "",
    createDate: "2023-09-04 16:56:39",
    updateBy: "",
    updateDate: "2023-09-04 16:56:39",
    flagDel: 0,
    remark: "",
  },
  {
    id: "***********62706222",
    menuCode: "PLAT_TENANT_LIST_ADD",
    menuName: "添加",
    menuCss: "",
    menuType: 1,
    menuPid: "***********62706221",
    menuUrl: "",
    menuActive: "",
    menuInitCert: "",
    menuNameEn: "ADD",
    menuAlwaysShow: 0,
    menuComp: "iframeCom/index",
    menuHidden: 0,
    menuCache: 1,
    menuLink: "",
    menuTitle: "",
    menuQuery: "",
    menuMark: "tenant:add",
    serverCode: "SecPlatWeb",
    menuSort: 0,
    createBy: "",
    createDate: "2023-09-04 16:56:39",
    updateBy: "",
    updateDate: "2023-09-04 16:56:39",
    flagDel: 0,
    remark: "",
  },
];
export const checkedMenu = [
  {
    label: "配置管理",
    labelEn: "Config Manage",
    children: [
      {
        label: "配置列表",
        labelEn: "Config List",
        children: [
          {
            label: "历史记录",
            labelEn: "Config History",
            children: [],
            pid: "***********62706211",
            id: "***********62706212",
            checked: false,
          },
          {
            label: "修改",
            labelEn: "UPDATE",
            children: [],
            pid: "***********62706211",
            id: "***********62706213",
            checked: false,
          },
        ],
        pid: "***********62706210",
        id: "***********62706211",
        checked: false,
      },
    ],
    pid: "",
    id: "***********62706210",
    checked: false,
  },
  {
    label: "设备授权管理",
    labelEn: "Device Authorization",
    children: [
      {
        label: "生成授权码",
        labelEn: "Generate Authorization Code",
        children: [
          {
            label: "获取授权码",
            labelEn: "Generate AuthCode",
            children: [],
            pid: "***********62706215",
            id: "***********62706216",
            checked: false,
          },
        ],
        pid: "***********62706214",
        id: "***********62706215",
        checked: false,
      },
      {
        label: "设备公钥管理",
        labelEn: "Device Public Key Management",
        children: [
          {
            label: "设备公钥下发",
            labelEn: "Device PK Add",
            children: [],
            pid: "***********62706217",
            id: "***********62706218",
            checked: false,
          },
          {
            label: "清空设备公钥",
            labelEn: "Delete Device Pk",
            children: [],
            pid: "***********62706217",
            id: "***********62706219",
            checked: false,
          },
        ],
        pid: "***********62706214",
        id: "***********62706217",
        checked: false,
      },
    ],
    pid: "",
    id: "***********62706214",
    checked: false,
  },
  {
    label: "租户管理",
    labelEn: "Tenant Manage",
    children: [
      {
        label: "租户列表",
        labelEn: "Tenant List",
        children: [
          {
            label: "添加",
            labelEn: "ADD",
            children: [],
            pid: "***********62706221",
            id: "***********62706222",
            checked: true,
          },
          {
            label: "编辑",
            labelEn: "EDIT",
            children: [],
            pid: "***********62706221",
            id: "***********62706223",
            checked: true,
          },
          {
            label: "删除",
            labelEn: "DELETE",
            children: [],
            pid: "***********62706221",
            id: "***********62706224",
            checked: true,
          },
          {
            label: "初始化",
            labelEn: "INIT",
            children: [],
            pid: "***********62706221",
            id: "***********62706225",
            checked: true,
          },
          {
            label: "人员信息",
            labelEn: "PERSON VIEW",
            children: [],
            pid: "***********62706221",
            id: "***********62706226",
            checked: true,
          },
        ],
        pid: "***********62706220",
        id: "***********62706221",
        checked: true,
      },
    ],
    pid: "",
    id: "***********62706220",
    checked: true,
  },
  {
    label: "日志管理",
    labelEn: "Log Manage",
    children: [
      {
        label: "日志列表设置",
        labelEn: "Log Config",
        children: [
          {
            label: "编辑",
            labelEn: "EDIT",
            children: [],
            pid: "***********62706228",
            id: "***********62706229",
            checked: false,
          },
        ],
        pid: "***********62706227",
        id: "***********62706228",
        checked: false,
      },
      {
        label: "syslog-ng设置",
        labelEn: "syslog-ng Config",
        children: [
          {
            label: "新增",
            labelEn: "ADD",
            children: [],
            pid: "***********62706230",
            id: "***********62706231",
            checked: false,
          },
          {
            label: "编辑",
            labelEn: "EDIT",
            children: [],
            pid: "***********62706230",
            id: "***********62706232",
            checked: false,
          },
          {
            label: "全量更新",
            labelEn: "UPDATE",
            children: [],
            pid: "***********62706230",
            id: "***********62706233",
            checked: false,
          },
          {
            label: "删除",
            labelEn: "DELETE",
            children: [],
            pid: "***********62706230",
            id: "***********62706234",
            checked: false,
          },
        ],
        pid: "***********62706227",
        id: "***********62706230",
        checked: false,
      },
      {
        label: "审计日志列表",
        labelEn: "Audit List",
        children: [
          {
            label: "验签",
            labelEn: "SIGN",
            children: [],
            pid: "16986210***********",
            id: "***********62706236",
            checked: false,
          },
          {
            label: "审计",
            labelEn: "CHECK",
            children: [],
            pid: "16986210***********",
            id: "***********62706237",
            checked: false,
          },
          {
            label: "详情",
            labelEn: "DETAIL",
            children: [],
            pid: "16986210***********",
            id: "***********62706238",
            checked: false,
          },
          {
            label: "下载全部日志",
            labelEn: "DOWNLOAD ALL LOG",
            children: [],
            pid: "16986210***********",
            id: "***********62706239",
            checked: false,
          },
        ],
        pid: "***********62706227",
        id: "16986210***********",
        checked: false,
      },
      {
        label: "业务日志列表",
        labelEn: "Business List",
        children: [
          {
            label: "下载当前日志",
            labelEn: "DOWNLOAD CURRENT LOG",
            children: [],
            pid: "***********62706240",
            id: "***********62706241",
            checked: false,
          },
        ],
        pid: "***********62706227",
        id: "***********62706240",
        checked: false,
      },
    ],
    pid: "",
    id: "***********62706227",
    checked: false,
  },
  {
    label: "CA管理",
    labelEn: "CA List",
    children: [
      {
        label: "三方CA",
        labelEn: "Know CA",
        children: [
          {
            label: "新增",
            labelEn: "ADD",
            children: [],
            pid: "***********62706243",
            id: "***********62706244",
            checked: false,
          },
          {
            label: "编辑",
            labelEn: "EDIT",
            children: [],
            pid: "***********62706243",
            id: "***********62706245",
            checked: false,
          },
          {
            label: "删除",
            labelEn: "DELETE",
            children: [],
            pid: "***********62706243",
            id: "***********62706246",
            checked: false,
          },
          {
            label: "下载",
            labelEn: "DOWNLOAD",
            children: [],
            pid: "***********62706243",
            id: "***********62706247",
            checked: false,
          },
        ],
        pid: "***********62706242",
        id: "***********62706243",
        checked: false,
      },
      {
        label: "自签CA",
        labelEn: "Local CA",
        children: [
          {
            label: "签发历史",
            labelEn: "Sign History",
            children: [
              {
                label: "签发历史下载",
                labelEn: "SIGN HISTORY DOWNLOAD",
                children: [],
                pid: "***********62706249",
                id: "***********62706250",
                checked: false,
              },
            ],
            pid: "***********62706248",
            id: "***********62706249",
            checked: false,
          },
          {
            label: "新增",
            labelEn: "ADD",
            children: [],
            pid: "***********62706248",
            id: "***********62706251",
            checked: false,
          },
          {
            label: "编辑",
            labelEn: "EDIT",
            children: [],
            pid: "***********62706248",
            id: "***********62706252",
            checked: false,
          },
          {
            label: "删除",
            labelEn: "DELETE",
            children: [],
            pid: "***********62706248",
            id: "***********62706253",
            checked: false,
          },
          {
            label: "签发请求",
            labelEn: "SIGN REQUEST",
            children: [],
            pid: "***********62706248",
            id: "***********62706254",
            checked: false,
          },
          {
            label: "下载CA证书",
            labelEn: "DOWNLOAD CA",
            children: [],
            pid: "***********62706248",
            id: "***********62706255",
            checked: false,
          },
        ],
        pid: "***********62706242",
        id: "***********62706248",
        checked: false,
      },
    ],
    pid: "",
    id: "***********62706242",
    checked: false,
  },
  {
    label: "SSL管理",
    labelEn: "SSL Manage",
    children: [
      {
        label: "SSL列表",
        labelEn: "SSL List",
        children: [
          {
            label: "新增",
            labelEn: "ADD",
            children: [],
            pid: "***********62706257",
            id: "***********62706258",
            checked: false,
          },
          {
            label: "编辑",
            labelEn: "EDIT",
            children: [],
            pid: "***********62706257",
            id: "***********62706259",
            checked: false,
          },
          {
            label: "导入PKCS12",
            labelEn: "IMPORT PKCS",
            children: [],
            pid: "***********62706257",
            id: "***********62706260",
            checked: false,
          },
          {
            label: "导入CRL",
            labelEn: "IMPORT CRL",
            children: [],
            pid: "***********62706257",
            id: "***********62706261",
            checked: false,
          },
          {
            label: "安装证书",
            labelEn: "INSTALL CERT",
            children: [],
            pid: "***********62706257",
            id: "***********62706262",
            checked: false,
          },
          {
            label: "下载证书",
            labelEn: "DOWNLOAD CERT",
            children: [],
            pid: "***********62706257",
            id: "***********62706263",
            checked: false,
          },
          {
            label: "删除",
            labelEn: "DELETE",
            children: [],
            pid: "***********62706257",
            id: "***********62706264",
            checked: false,
          },
        ],
        pid: "***********62706256",
        id: "***********62706257",
        checked: false,
      },
    ],
    pid: "",
    id: "***********62706256",
    checked: false,
  },
  {
    label: "网络管理",
    labelEn: "Network Manage",
    children: [
      {
        label: "网卡列表",
        labelEn: "Card List",
        children: [
          {
            label: "绑定网卡",
            labelEn: "BIND CARD",
            children: [],
            pid: "***********62706266",
            id: "***********62706267",
            checked: false,
          },
          {
            label: "编辑",
            labelEn: "EDIT",
            children: [],
            pid: "***********62706266",
            id: "***********62706268",
            checked: false,
          },
          {
            label: "解绑",
            labelEn: "Unbound",
            children: [],
            pid: "***********62706266",
            id: "***********62706269",
            checked: false,
          },
          {
            label: "详情",
            labelEn: "DETAIL",
            children: [],
            pid: "***********62706266",
            id: "***********62706270",
            checked: false,
          },
          {
            label: "设为管理网卡",
            labelEn: "Set As Manage",
            children: [],
            pid: "***********62706266",
            id: "***********62706271",
            checked: false,
          },
        ],
        pid: "***********62706265",
        id: "***********62706266",
        checked: false,
      },
      {
        label: "DNS设置",
        labelEn: "DNS Config",
        children: [
          {
            label: "新增",
            labelEn: "ADD",
            children: [],
            pid: "***********62706272",
            id: "***********62706273",
            checked: false,
          },
          {
            label: "删除",
            labelEn: "DELETE",
            children: [],
            pid: "***********62706272",
            id: "***********62706274",
            checked: false,
          },
        ],
        pid: "***********62706265",
        id: "***********62706272",
        checked: false,
      },
      {
        label: "路由设置",
        labelEn: "Route Config",
        children: [
          {
            label: "新增",
            labelEn: "ADD",
            children: [],
            pid: "***********62706275",
            id: "***********62706276",
            checked: false,
          },
          {
            label: "删除",
            labelEn: "DELETE",
            children: [],
            pid: "***********62706275",
            id: "***********62706277",
            checked: false,
          },
        ],
        pid: "***********62706265",
        id: "***********62706275",
        checked: false,
      },
      {
        label: "网络诊断",
        labelEn: "Network Diagnostics",
        children: [
          {
            label: "traceroute提交",
            labelEn: "TRACEROUTE SUBMIT",
            children: [],
            pid: "***********62706278",
            id: "***********62706279",
            checked: false,
          },
          {
            label: "ping提交",
            labelEn: "PING SUBMIT",
            children: [],
            pid: "***********62706278",
            id: "***********62706280",
            checked: false,
          },
          {
            label: "telnet提交",
            labelEn: "TELNET SUBMIT",
            children: [],
            pid: "***********62706278",
            id: "***********62706281",
            checked: false,
          },
        ],
        pid: "***********62706265",
        id: "***********62706278",
        checked: false,
      },
      {
        label: "网关设置",
        labelEn: "Gateway Setting",
        children: [
          {
            label: "提交",
            labelEn: "SUBMIT",
            children: [],
            pid: "***********62706282",
            id: "***********62706283",
            checked: false,
          },
        ],
        pid: "***********62706265",
        id: "***********62706282",
        checked: false,
      },
    ],
    pid: "",
    id: "***********62706265",
    checked: false,
  },
  {
    label: "系统设置",
    labelEn: "System Setting",
    children: [
      {
        label: "设置系统时间",
        labelEn: "Setting System Time",
        children: [
          {
            label: "提交",
            labelEn: "SUBMIT",
            children: [],
            pid: "***********62706285",
            id: "***********62706286",
            checked: false,
          },
        ],
        pid: "***********62706284",
        id: "***********62706285",
        checked: false,
      },
      {
        label: "设备重启",
        labelEn: "Device restart",
        children: [
          {
            label: "提交",
            labelEn: "SUBMIT",
            children: [],
            pid: "***********62706287",
            id: "***********62706288",
            checked: false,
          },
        ],
        pid: "***********62706284",
        id: "***********62706287",
        checked: false,
      },
      {
        label: "RAID检查",
        labelEn: "RAID Check",
        children: [],
        pid: "***********62706284",
        id: "***********62706289",
        checked: false,
      },
      {
        label: "升级回滚",
        labelEn: "Upgrade",
        children: [
          {
            label: "升级",
            labelEn: "UPGRADE",
            children: [],
            pid: "***********62706290",
            id: "***********62706291",
            checked: false,
          },
          {
            label: "回滚",
            labelEn: "ROLLBACK",
            children: [],
            pid: "***********62706290",
            id: "***********62706292",
            checked: false,
          },
        ],
        pid: "***********62706284",
        id: "***********62706290",
        checked: false,
      },
      {
        label: "主题风格",
        labelEn: "Thematic Style",
        children: [],
        pid: "***********62706284",
        id: "***********62706293",
        checked: false,
      },
      {
        label: "WEB证书管理",
        labelEn: "WEB Certificate Management",
        children: [],
        pid: "***********62706284",
        id: "***********62706294",
        checked: false,
      },
    ],
    pid: "",
    id: "***********62706284",
    checked: false,
  },
  {
    label: "监控管理",
    labelEn: "Monitor Manage",
    children: [
      {
        label: "SNMP用户",
        labelEn: "User List",
        children: [
          {
            label: "新增",
            labelEn: "ADD",
            children: [],
            pid: "***********62706296",
            id: "***********62706297",
            checked: false,
          },
          {
            label: "同步SNMP",
            labelEn: "ASYN SNMP",
            children: [],
            pid: "***********62706296",
            id: "***********62706298",
            checked: false,
          },
          {
            label: "编辑",
            labelEn: "EDIT",
            children: [],
            pid: "***********62706296",
            id: "***********62706299",
            checked: false,
          },
          {
            label: "删除",
            labelEn: "DELETE",
            children: [],
            pid: "***********62706296",
            id: "***********62706300",
            checked: false,
          },
        ],
        pid: "***********62706295",
        id: "***********62706296",
        checked: false,
      },
      {
        label: "SNMP权限",
        labelEn: "Role List",
        children: [
          {
            label: "新增",
            labelEn: "ADD",
            children: [],
            pid: "***********62706301",
            id: "***********62706302",
            checked: false,
          },
          {
            label: "编辑",
            labelEn: "EDIT",
            children: [],
            pid: "***********62706301",
            id: "***********62706303",
            checked: false,
          },
          {
            label: "同步SNMP",
            labelEn: "ASYNC SNMP",
            children: [],
            pid: "***********62706301",
            id: "***********62706304",
            checked: false,
          },
          {
            label: "删除",
            labelEn: "DELETE",
            children: [],
            pid: "***********62706301",
            id: "***********62706305",
            checked: false,
          },
        ],
        pid: "***********62706295",
        id: "***********62706301",
        checked: false,
      },
      {
        label: "展示页面",
        labelEn: "VIEW Page",
        children: [],
        pid: "***********62706295",
        id: "***********62706306",
        checked: false,
      },
    ],
    pid: "",
    id: "***********62706295",
    checked: false,
  },
  {
    label: "备份管理",
    labelEn: "Backup Manage",
    children: [
      {
        label: "备份列表",
        labelEn: "Backup List",
        children: [
          {
            label: "新增",
            labelEn: "ADD",
            children: [],
            pid: "***********62706308",
            id: "***********62706309",
            checked: false,
          },
          {
            label: "详情",
            labelEn: "DETAIL",
            children: [],
            pid: "***********62706308",
            id: "***********62706310",
            checked: false,
          },
          {
            label: "下载",
            labelEn: "DOWNLOAD",
            children: [],
            pid: "***********62706308",
            id: "***********62706311",
            checked: false,
          },
          {
            label: "恢复",
            labelEn: "RECOVERY",
            children: [],
            pid: "***********62706308",
            id: "***********62706312",
            checked: false,
          },
          {
            label: "删除",
            labelEn: "DELETE",
            children: [],
            pid: "***********62706308",
            id: "***********62706313",
            checked: false,
          },
        ],
        pid: "***********62706307",
        id: "***********62706308",
        checked: false,
      },
      {
        label: "恢复列表",
        labelEn: "Recovery List",
        children: [
          {
            label: "新增",
            labelEn: "ADD",
            children: [],
            pid: "***********62706314",
            id: "***********62706315",
            checked: false,
          },
          {
            label: "详情",
            labelEn: "DETAIL",
            children: [],
            pid: "***********62706314",
            id: "***********62706316",
            checked: false,
          },
          {
            label: "取消管理网卡",
            labelEn: "Cancel As Manage",
            children: [],
            pid: "***********62706314",
            id: "***********62706317",
            checked: false,
          },
        ],
        pid: "***********62706307",
        id: "***********62706314",
        checked: false,
      },
      {
        label: "定时备份",
        labelEn: "Scheduled backup",
        children: [
          {
            label: "新增",
            labelEn: "ADD",
            children: [],
            pid: "***********62706318",
            id: "***********62706319",
            checked: false,
          },
          {
            label: "编辑",
            labelEn: "EDIT",
            children: [],
            pid: "***********62706318",
            id: "***********62706320",
            checked: false,
          },
          {
            label: "删除",
            labelEn: "DELETE",
            children: [],
            pid: "***********62706318",
            id: "***********62706321",
            checked: false,
          },
        ],
        pid: "***********62706307",
        id: "***********62706318",
        checked: false,
      },
      {
        label: "远程备份管理",
        labelEn: "Remote backup",
        children: [],
        pid: "***********62706307",
        id: "***********62706322",
        checked: false,
      },
    ],
    pid: "",
    id: "***********62706307",
    checked: false,
  },
  {
    label: "系统管理",
    labelEn: "System Manage",
    children: [
      {
        label: "用户列表",
        labelEn: "User List",
        children: [
          {
            label: "新增",
            labelEn: "ADD",
            children: [],
            pid: "***********62706324",
            id: "***********62706325",
            checked: true,
          },
          {
            label: "修改",
            labelEn: "UPDATE",
            children: [],
            pid: "***********62706324",
            id: "***********62706326",
            checked: true,
          },
          {
            label: "删除",
            labelEn: "DELETE",
            children: [],
            pid: "***********62706324",
            id: "***********62706327",
            checked: true,
          },
          {
            label: "查询",
            labelEn: "QUERY",
            children: [],
            pid: "***********62706324",
            id: "***********62706328",
            checked: true,
          },
        ],
        pid: "***********62706323",
        id: "***********62706324",
        checked: true,
      },
      {
        label: "角色列表",
        labelEn: "Role List",
        children: [
          {
            label: "新增",
            labelEn: "ADD",
            children: [],
            pid: "***********62706329",
            id: "***********62706330",
            checked: true,
          },
          {
            label: "修改",
            labelEn: "UPDATE",
            children: [],
            pid: "***********62706329",
            id: "***********62706331",
            checked: true,
          },
          {
            label: "删除",
            labelEn: "DELETE",
            children: [],
            pid: "***********62706329",
            id: "***********62706332",
            checked: true,
          },
          {
            label: "查询",
            labelEn: "QUERY",
            children: [],
            pid: "***********62706329",
            id: "***********62706333",
            checked: true,
          },
        ],
        pid: "***********62706323",
        id: "***********62706329",
        checked: true,
      },
    ],
    pid: "",
    id: "***********62706323",
    checked: true,
  },
  {
    label: "告警规则管理",
    labelEn: "Alarm rule Manage",
    children: [
      {
        label: "告警规则列表",
        labelEn: "Warn Rule List",
        children: [
          {
            label: "编辑",
            labelEn: "EDIT",
            children: [],
            pid: "***********62706335",
            id: "***********62706336",
            checked: false,
          },
          {
            label: "删除",
            labelEn: "DELETE",
            children: [],
            pid: "***********62706335",
            id: "***********62706337",
            checked: false,
          },
          {
            label: "配置联系人",
            labelEn: "Cofig Contact",
            children: [
              {
                label: "添加",
                labelEn: "ADD",
                children: [],
                pid: "***********62706338",
                id: "***********62706339",
                checked: false,
              },
              {
                label: "编辑",
                labelEn: "EDIT",
                children: [],
                pid: "***********62706338",
                id: "***********62706340",
                checked: false,
              },
              {
                label: "删除",
                labelEn: "DELETE",
                children: [],
                pid: "***********62706338",
                id: "***********62706341",
                checked: false,
              },
            ],
            pid: "***********62706335",
            id: "***********62706338",
            checked: false,
          },
        ],
        pid: "***********62706334",
        id: "***********62706335",
        checked: false,
      },
      {
        label: "联系人列表",
        labelEn: "Contact List",
        children: [
          {
            label: "新增",
            labelEn: "ADD",
            children: [],
            pid: "***********62706342",
            id: "***********62706343",
            checked: false,
          },
          {
            label: "编辑",
            labelEn: "EDIT",
            children: [],
            pid: "***********62706342",
            id: "***********62706344",
            checked: false,
          },
          {
            label: "删除",
            labelEn: "DELETE",
            children: [],
            pid: "***********62706342",
            id: "***********62706345",
            checked: false,
          },
        ],
        pid: "***********62706334",
        id: "***********62706342",
        checked: false,
      },
      {
        label: "告警列表",
        labelEn: "Warn List",
        children: [
          {
            label: "详情",
            labelEn: "DETAIL",
            children: [],
            pid: "***********62706346",
            id: "***********62706347",
            checked: false,
          },
        ],
        pid: "***********62706334",
        id: "***********62706346",
        checked: false,
      },
      {
        label: "邮件服务器配置",
        labelEn: "Email Server Config",
        children: [
          {
            label: "重置",
            labelEn: "RESET",
            children: [],
            pid: "***********62706348",
            id: "***********62706349",
            checked: false,
          },
          {
            label: "提交",
            labelEn: "SUBMIT",
            children: [],
            pid: "***********62706348",
            id: "***********62706350",
            checked: false,
          },
          {
            label: "发送测试邮件",
            labelEn: "SENDCHECKEMAIL",
            children: [],
            pid: "***********62706348",
            id: "***********62706351",
            checked: false,
          },
        ],
        pid: "***********62706334",
        id: "***********62706348",
        checked: false,
      },
    ],
    pid: "",
    id: "***********62706334",
    checked: false,
  },
];
export const menu = [
  //   {
  //     alwaysShow: true,
  //     component: "Layout",
  //     hidden: false,
  //     meta: {
  //       title: "身份管理",
  //       titleEn: "System Manage",
  //       icon: "businessGroup",
  //       noCache: true,
  //       link: "",
  //       activeMenu: "",
  //     },
  //     name: "System",
  //     path: "/identity",
  //     children: [
  //       {
  //         alwaysShow: false,
  //         component: "identity/organization/index",
  //         hidden: false,
  //         meta: {
  //           title: "组织机构",
  //           titleEn: "Organization List",
  //           icon: "",
  //           noCache: true,
  //           link: "",
  //           activeMenu: "",
  //         },
  //         name: "organizationList",
  //         path: "organization",
  //         children: [],
  //         query: "",
  //         pid: "***********62706323",
  //         id: "***********62706329",
  //       },
  //       {
  //         alwaysShow: false,
  //         component: "identity/user/index",
  //         hidden: false,
  //         meta: {
  //           title: "用户管理",
  //           titleEn: "User List",
  //           icon: "",
  //           noCache: true,
  //           link: "",
  //           activeMenu: "",
  //         },
  //         name: "userList",
  //         path: "user",
  //         children: [],
  //         query: "",
  //         pid: "***********62706323",
  //         id: "***********62706329",
  //       },
  //       {
  //         alwaysShow: false,
  //         component: "identity/role/index",
  //         hidden: false,
  //         meta: {
  //           title: "角色管理",
  //           titleEn: "Role List",
  //           icon: "",
  //           noCache: true,
  //           link: "",
  //           activeMenu: "",
  //         },
  //         name: "roleList",
  //         path: "role",
  //         children: [],
  //         query: "",
  //         pid: "***********62706323",
  //         id: "***********62706329",
  //         children: [
  //           {
  //             alwaysShow: false,
  //             component: "identity/role/appList",
  //             hidden: true,
  //             meta: {
  //               title: "应用列表",
  //               titleEn: "Application List",
  //               icon: "",
  //               noCache: true,
  //               link: "",
  //               activeMenu: "",
  //             },
  //             name: "RoleAppList",
  //             path: "appList",
  //             children: [],
  //             query: "",
  //             pid: "1699715782936981506",
  //             id: "1699715782936981507",
  //           },
  //         ],
  //       },
  //     ],
  //     query: "",
  //     pid: "",
  //     id: "***********62706323",
  //   },
];
export const dict = {
  userVerification: [
    {
      typeCode: "userVerification",
      code: "false",
      name: "失败",
    },
    {
      typeCode: "userVerification",
      code: "true",
      name: "成功",
    },
  ],
};
