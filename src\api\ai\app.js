import request from "@/utils/request";
import config from "../configs";

let baseUrl = `${config.PROJAPI}/hub/app`;

export function getAppList(data) {
  return request({
    url: `${baseUrl}/getAppList`,
    method: "post",
    data,
  });
}

export function getAppDifyUrl(data) {
  return request({
    url: `${baseUrl}/getIframeUrl`,
    method: "post",
    headers: {
      repeatSubmit: false,
    },
    data,
  });
}

export function getAppDetail(data) {
  return request({
    url: `${baseUrl}/getAppDetail`,
    method: "post",
    data,
  });
}
