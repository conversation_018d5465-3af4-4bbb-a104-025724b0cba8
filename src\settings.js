module.exports = {
  webTitle: process.env.VUE_APP_TITLE || "", // 网页标题
  /**
   * 侧边栏主题 深色主题theme-dark，浅色主题theme-light
   */
  sideTheme: "theme-custom",

  /**
   * 是否系统布局配置
   */
  showSettings: false,

  /**
   * 是否显示顶部导航
   */
  topNav: false,

  /**
   * 是否显示 tagsView
   */
  tagsView: true,

  /**
   * 是否固定头部
   */
  fixedHeader: false,

  /**
   * 是否显示logo
   */
  sidebarLogo: true,

  /**
   * 是否显示动态标题
   */
  dynamicTitle: false,

  // 自定义主题(默认深色主题)
  // menuColorCustom: '#bfcbd9',
  // menuBackgroundCustom: '#1C2439',
  // menuColorActiveCustom: '#fff',
  // subMenuBackgroundCustom: '#000000',
  // subMenuBackgroundActiveCustom: '#1C6CDD',
  // subMenuHoverCustom: 'rgba(28, 108, 221, 0.5)',
  // topBackgroundCustom: '#fff',
  // topSvgCustom: '#231815',

  // 自定义主题(默认浅色主题)
  menuColorCustom: "#000",
  menuBackgroundCustom: "#fff",
  menuColorActiveCustom: "#fff",
  subMenuBackgroundCustom: "#fff",
  subMenuBackgroundActiveCustom: "#1C6CDD",
  subMenuHoverCustom: "#1C6CDD19",
  topBackgroundCustom: "#fff",
  topSvgCustom: "#231815",

  // 自定义logo
  isTitleLogo: true, // title是否为文字,否则为图片
  titleLogo: process.env.VUE_APP_TITLE || "智能门户", // 文字title
  titleLogoColor: "#001529",
  titleLogoFontStyle: false,

  /**
   * @type {string | array} 'production' | ['production', 'development']
   * @description Need show err logs component.
   * The default is only used in the production env
   * If you want to also use it in dev, you can pass ['production', 'development']
   */
  errorLog: "production",
};
