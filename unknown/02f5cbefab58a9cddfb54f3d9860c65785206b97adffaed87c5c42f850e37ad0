<template>
  <div class="themePanel-container">
    <div class="themePanel-container-part">
      <div class="themePanel-scroll">
        <div class="drawer-container" v-loading="loading">
          <!-- 当前主题 -->
          <div class="setting-drawer-content">
            <div class="setting-drawer-head">
              <h2 class="drawer-head">
                当前主题：{{ isZh() ? themeNameZh : themeNameEn }}
              </h2>
              <!-- <div>{{ currentThemeObj }}</div> -->
              <!-- 导出/保存/重置/回到配置列表 -->
              <div class="drawer-head-btns">
                <el-button
                  size="small"
                  class="search-btn"
                  type="primary"
                  style="width: 160px"
                  @click="onSetDefaultDig"
                  v-hasPermi="['system:theme:saveTheme']"
                  >{{ $t("themeConfig.setDefaultTheme") }}</el-button
                >
                <el-button
                  size="small"
                  class="search-btn"
                  type="primary"
                  @click="exportSetting"
                  >{{ $t("common.export") }}</el-button
                >
                <el-button
                  v-if="!isFromConfig"
                  size="small"
                  class="search-btn"
                  type="primary"
                  @click="saveSetting"
                  v-hasPermi="['system:theme:saveTheme']"
                  >{{ $t("common.save") }}</el-button
                >
                <!-- <el-button
                  v-if="!isFromConfig"
                  size="small"
                  class="search-btn cancel-btn"
                  @click="resetSetting"
                  >{{ $t("common.reset") }}</el-button
                > -->
                <el-button
                  v-if="isFromConfig"
                  size="small"
                  class="search-btn cancel-btn"
                  @click="onClickConfig"
                  v-hasPermi="['system:theme:saveTheme']"
                  >{{ $t("themeConfig.goBackConfig") }}</el-button
                >
              </div>
            </div>
          </div>
          <!-- <div style="padding: 0 24px">
            <el-tooltip
              class="item"
              effect="dark"
              placement="right"
              popper-class="settings-tip-class"
              :content="$t('themeConfig.isCoverSystemTip')"
            >
              <el-checkbox v-model="isCoverSystem">{{
                $t("themeConfig.isCoverSystem")
              }}</el-checkbox>
            </el-tooltip>
          </div> -->
          <el-divider />
          <!-- 系统主题 -->
          <div class="setting-drawer-content">
            <div class="setting-drawer-title">
              <h3 class="drawer-title">系统主题</h3>
            </div>
            <ThemeSystem
              :theme="theme"
              :isCustom="isCustom"
              :addLoading="addLoading"
              :dialogVisible="dialogVisible"
              :currentThemeId="currentThemeId"
              :defaultThemeId="defaultThemeId"
              :defaultThemeObj="defaultThemeObj"
              :sideTheme="sideTheme"
              :themeAll="themeAll"
              @handleTheme="handleTheme"
              @addTheme="addTheme"
              @deleteTheme="deleteTheme"
              @eventClose="eventClose"
              @openDig="openDig"
            ></ThemeSystem>
          </div>
          <el-divider />
          <!-- 自定义主题 -->
          <div class="setting-drawer-content">
            <div class="setting-drawer-title">
              <h3 class="drawer-title">自定义主题</h3>
            </div>
            <!-- 主题颜色 -->
            <div class="drawer-item">
              <div class="drawer-item-title">主题颜色</div>
              <theme-picker
                @change="themeChange"
                class="drawer-item-theme-color drawer-item"
              />
            </div>
            <!-- 侧边导航栏 -->
            <div class="drawer-item">
              <div class="drawer-item-title">菜单栏</div>
              <!-- dark/light/theme/高级配置 -->
              <el-radio-group v-model="currentThemeClass" class="drawer-item">
                <el-radio label="theme-dark"> 暗色模式 </el-radio>
                <el-radio label="theme-light"> 亮色模式 </el-radio>
                <el-radio label="theme-theme"> 跟随颜色 </el-radio>
                <el-radio label="theme-custom"> 自定义 </el-radio>
              </el-radio-group>
              <sidebar-custom
                class="drawer-item"
                :theme="theme"
                :sideTheme="sideTheme"
                :currentThemeClass="currentThemeClass"
                :currentThemeObj="currentThemeObj"
                @handleTheme="handleTheme"
                @setColor="setColor"
              ></sidebar-custom>
            </div>
          </div>
          <!-- 顶部导航 -->
          <div class="drawer-item">
            <div class="drawer-item-title">顶部导航</div>
            <TopNavCustom
              @setColor="setColor"
              class="drawer-item"
            ></TopNavCustom>
          </div>

          <!-- 布局配置 -->
          <div class="drawer-item">
            <div class="drawer-item-title">布局配置</div>
            <!-- Tags导航 -->
            <div class="drawer-item">
              <span>Tags导航</span>
              <el-switch v-model="tagsView" class="drawer-switch" />
            </div>
            <!-- 展示Logo -->
            <div class="drawer-item">
              <span>展示Logo</span>
              <el-switch v-model="sidebarLogo" class="drawer-switch" />
            </div>
            <!-- 动态标题 -->
            <div class="drawer-item">
              <span>动态标题</span>
              <el-switch v-model="dynamicTitle" class="drawer-switch" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <custom-to-sys-dig
      :toSysDigVisible="toSysDigVisible"
      @eventClose="eventClose"
      @addThemeNameAndSave="addThemeNameAndSave"
    ></custom-to-sys-dig>
    <save-default-theme-dig
      :dialogVisible="configDig"
      :defaultThemeId="defaultThemeId"
      :themeAll="themeAll"
      @eventClose="eventClose"
      @onSaveDefaultTheme="onSaveDefaultTheme"
    />
  </div>
</template>

<script>
// import {
//   getThemeList,
//   importThemeFile,
//   deleteTheme,
//   getDefaultThemeObj,
// } from "@/api/theme";

import { mapActions } from "vuex";
import ThemePicker from "@/components/ThemePicker"; // 自定义主题颜色
import LogoCustom from "./components/logoCustom.vue";
import ThemeSystem from "./components/themeSystem.vue"; // 选择系统主题
import SidebarCustom from "./components/sidebarCustom.vue"; // 自定义侧边栏
import TopNavCustom from "./components/topNavCustom.vue"; // 自定义顶导航栏
import CustomToSysDig from "./components/customToSysDig.vue"; // 自定义主题转系统主题弹窗
import saveDefaultThemeDig from "./components/saveDefaultThemeDig.vue";
import { formatColor_deep } from "@/utils/index"; // theme转换样式颜色
import beautifier from "js-beautify";
import { getFileMd5, getUploadUid } from "@/utils/util";
export default {
  name: "ThemeConfig",
  components: {
    ThemePicker,
    LogoCustom,
    ThemeSystem,
    SidebarCustom,
    TopNavCustom,
    CustomToSysDig,
    saveDefaultThemeDig,
  },
  data() {
    return {
      loading: false,
      addLoading: false,
      isCoverSystem: localStorage.getItem("isCoverSystem") == "true", // 是否覆盖默认主题
      themeSystem: JSON.parse(
        JSON.stringify(this.$store.state.settings.themeSystem),
      ), // 默认系统主题[]
      themeServer: [], // 接口系统主题
      //   themeOthers: JSON.parse(localStorage.getItem("theme-others")) || [], // 其他系统主题(本地)
      themeOthers: [], // 其他系统主题（本地）
      themeAll: [], // 全部系统主题
      currentThemeId: "",
      defaultThemeId: "",
      defaultThemeObj: {},
      themeNameEn: this.$store.state.settings.themeNameEn,
      themeNameZh: this.$store.state.settings.themeNameZh,
      theme: this.$store.state.settings.theme,
      sideTheme: this.$store.state.settings.sideTheme,
      webTitle: this.$store.state.settings.webTitle,
      // 自定义颜色
      menuColorCustom: this.$store.state.settings.menuColorCustom,
      menuBackgroundCustom: this.$store.state.settings.menuBackgroundCustom,
      menuColorActiveCustom: this.$store.state.settings.menuColorActiveCustom,
      subMenuBackgroundActiveCustom:
        this.$store.state.settings.subMenuBackgroundActiveCustom,
      subMenuBackgroundCustom:
        this.$store.state.settings.subMenuBackgroundCustom,
      subMenuHoverCustom: this.$store.state.settings.subMenuHoverCustom,
      topBackgroundCustom: this.$store.state.settings.topBackgroundCustom,
      topSvgCustom: this.$store.state.settings.topSvgCustom,
      // 自定义logo
      logoIcon: this.$store.state.settings.logoIcon,
      logoIconName: this.$store.state.settings.logoIconName,
      isTitleLogo: this.$store.state.settings.isTitleLogo,
      titleLogo: this.$store.state.settings.titleLogo,
      titleLogoColor: this.$store.state.settings.titleLogoColor,
      titleLogoFontStyle: this.$store.state.settings.titleLogoFontStyle,
      imageLogo: this.$store.state.settings.imageLogo,
      imageLogoName: this.$store.state.settings.imageLogoName,
      // 侧边栏:root全局样式
      rootCssList: {
        menuColorCustom: "--custom-menu-color",
        menuBackgroundCustom: "--custom-menu-background",
        menuColorActiveCustom: "--custom-menu-color-active",
        subMenuBackgroundActiveCustom: "--custom-sub-menu-background-active",
        subMenuBackgroundCustom: "--custom-sub-menu-background",
        subMenuHoverCustom: "--custom-sub-menu-hover",
        topBackgroundCustom: "--custom-top-bg",
        topSvgCustom: "--custom-top-svg",
      },
      currentThemeClass: this.$store.state.settings.sideTheme, // sidebar主题类名
      currentThemeObj_sys: {}, // 当前系统主题_sys,和currentThemeObj当前主题区分
      currentThemeObj_copy: {}, // 离开本页面之前用于判断是否需要提示用户保存配置
      isCustom: false,
      toSysDigVisible: false,
      dialogVisible: false,

      logoDigVisible: false,
      addType: false, // 新系统主题存储方式,true接口,false本地theme-others
      // deleteType: false, // 删除新系统主题的方式,同上
      configDig: false,
      rowConfig: null,
    };
  },
  computed: {
    // 如果该页面从 配置列表-编辑-更多系统主题 跳转进来, 则不显示保存、重置按钮
    isFromConfig() {
      return this.$route.params.type == "config";
    },
    rowObj() {
      return this.$route.params.rowObj;
    },
    currentThemeObj() {
      return {
        webTitle: this.webTitle,
        themeNameZh: this.themeNameZh,
        themeNameEn: this.themeNameEn,
        theme: this.theme,
        sideTheme: this.sideTheme,
        tagsView: this.tagsView,
        sidebarLogo: this.sidebarLogo,
        dynamicTitle: this.dynamicTitle,
        menuColorCustom: this.menuColorCustom,
        menuBackgroundCustom: this.menuBackgroundCustom,
        menuColorActiveCustom: this.menuColorActiveCustom,
        subMenuBackgroundCustom: this.subMenuBackgroundCustom,
        subMenuBackgroundActiveCustom: this.subMenuBackgroundActiveCustom,
        subMenuHoverCustom: this.subMenuHoverCustom,
        topBackgroundCustom: this.topBackgroundCustom,
        topSvgCustom: this.topSvgCustom,
      };
    },
    visible: {
      get() {
        return this.$store.state.settings.showSettings;
      },
    },
    fixedHeader: {
      get() {
        return this.$store.state.settings.fixedHeader;
      },
      set(val) {
        this.$store.dispatch("settings/changeSetting", {
          key: "fixedHeader",
          value: val,
        });
      },
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav;
      },
      set(val) {
        this.$store.dispatch("settings/changeSetting", {
          key: "topNav",
          value: val,
        });
        if (!val) {
          this.$store.dispatch("app/toggleSideBarHide", false);
          this.$store.commit(
            "SET_SIDEBAR_ROUTERS",
            this.$store.state.permission.defaultRoutes,
          );
        }
      },
    },
    tagsView: {
      get() {
        return this.$store.state.settings.tagsView;
      },
      set(val) {
        this.$store.dispatch("settings/changeSetting", {
          key: "tagsView",
          value: val,
        });
      },
    },
    sidebarLogo: {
      get() {
        return this.$store.state.settings.sidebarLogo;
      },
      set(val) {
        this.$store.dispatch("settings/changeSetting", {
          key: "sidebarLogo",
          value: val,
        });
      },
    },
    dynamicTitle: {
      get() {
        return this.$store.state.settings.dynamicTitle;
      },
      set(val) {
        this.$store.dispatch("settings/changeSetting", {
          key: "dynamicTitle",
          value: val,
        });
      },
    },
  },
  watch: {
    currentThemeClass(val) {
      this.attrChange("sideTheme", val);
      this.initAttr(this.sideTheme, this.theme);
    },
    theme(val) {
      this.initAttr(this.sideTheme, this.theme);
    },
    // 将当前主题改为自定义主题
    currentThemeObj: {
      handler(val) {
        if (!this.isCustom) {
          for (let key in this.currentThemeObj) {
            if (
              this.currentThemeObj_sys[key] !== undefined &&
              this.currentThemeObj[key] !== this.currentThemeObj_sys[key]
            ) {
              this.themeNameZh = "自定义主题";
              this.themeNameEn = "Custom Topic";
              this.currentThemeId = "000";
              this.attrChange("isCustom", true);
            }
          }
        }
      },
      // immediate: true,
      deep: true,
    },
  },
  async created() {
    this.loading = true;
    try {
      // await this.getDefaultThemeObj();
      await this.initThemeAll(true);
      this.loading = false;
    } catch {
      this.loading = false;
      //   this.themeAll = this.themeSystem;
    }
    // 初始化themeAll
    // 属性初始值从store中取,而样式并非一开始就储存在store,因此需手动赋初值
    this.initAttr(
      this.$store.state.settings.sideTheme,
      this.$store.state.settings.theme,
    );
    // 给当前系统主题_sys赋初值
    this.currentThemeObj_sys = JSON.parse(JSON.stringify(this.currentThemeObj));
    this.currentThemeObj_copy = JSON.parse(
      JSON.stringify(this.currentThemeObj),
    );
  },
  methods: {
    ...mapActions(["getSysImg"]),
    //   系统默认主题弹层
    onSetDefaultDig() {
      this.configDig = true;
    },
    // 设置默认主题
    async onSaveDefaultTheme() {
      window.location.reload();
    },
    // 回到配置列表
    onClickConfig() {
      this.$router.push({
        name: "configList",
        params: { rowObj: this.rowObj },
      });
    },
    formatColor_deep,
    onClickLogoSettings() {
      this.$router.push("/themeConfig/logoSettings");
    },
    // 自定义主题存为系统主题-弹窗
    onClickSaveAsSys(type) {
      this.toSysDigVisible = true;
      this.addType = type;
    },
    // 根据主题id获取系统图片
    async getSysImgById() {
      await this.getSysImg(this.currentThemeId);
    },
    // 获取系统配置
    async getDefaultThemeObj() {
      const res1 = await getDefaultThemeObj();
      res1.data.themeId = res1.data.themeId ? res1.data.themeId : "001";
      // 系统默认主题
      this.defaultThemeId = res1.data.themeId;
      // 当前主题
      let layoutData = JSON.parse(
        localStorage.getItem("layout-setting") || "{}",
      );
      this.currentThemeId = this.isCoverSystem
        ? layoutData.themeId || res1.data.themeId
        : res1.data.themeId;
      // TODO 切换时如何改变webTitle
      if (layoutData.webTitle) {
        this.attrChange("webTitle", layoutData.webTitle);
      } else if (res1.data.webTitle) {
        this.attrChange("webTitle", res1.data.webTitle);
      }
    },
    async initThemeAll(flag = false) {
      // const res2 = await getThemeList();

      // this.themeServer = res2.data;
      // 获取系统图片
      await this.getSysImgById();
      // 是否可删除
      this.themeSystem.forEach((obj) => {
        obj.isDelete = false;
      });
      this.themeOthers.forEach((obj) => {
        obj.isDelete = true;
      });
      this.themeServer.forEach((obj) => {
        obj.isDelete = true;
      });
      // 把系统默认主题放在最前面
      let themeAllNoSort = [
        ...JSON.parse(JSON.stringify(this.themeSystem)),
        ...JSON.parse(JSON.stringify(this.themeServer)),
        ...JSON.parse(JSON.stringify(this.themeOthers)),
      ];
      let defaultThemeObj = null;
      let themeFilter = themeAllNoSort.filter((item, i) => {
        if (item.themeId != this.defaultThemeId) {
          return true;
        } else {
          defaultThemeObj = item;
          return false;
        }
      });

      this.themeAll = defaultThemeObj
        ? [defaultThemeObj, ...themeFilter]
        : [...themeAllNoSort];
      this.setLeftColor(this.themeAll);
      // data store中的初始值需App.vue接口先加载,而子页面中无法保证已加载完成, 因此需重新加载data
      if (flag) {
        // 当前是否为自定义主题
        this.isCustom = this.currentThemeId == "000" ? true : false;
        if (!this.isCustom) {
          let obj = this.themeAll.find((item, i) => {
            return item.themeId == this.currentThemeId;
          });
          this.webTitle = obj.webTitle;
          this.themeNameEn = obj.themeNameEn;
          this.themeNameZh = obj.themeNameZh;
          this.theme = obj.theme;
          this.sideTheme = obj.sideTheme;
          this.menuColorCustom = obj.menuColorCustom;
          this.menuBackgroundCustom = obj.menuBackgroundCustom;
          this.menuColorActiveCustom = obj.menuColorActiveCustom;
          this.subMenuBackgroundActiveCustom =
            obj.subMenuBackgroundActiveCustom;
          this.subMenuBackgroundCustom = obj.subMenuBackgroundCustom;
          this.subMenuHoverCustom = obj.subMenuHoverCustom;
          this.topBackgroundCustom = obj.topBackgroundCustom;
          this.topSvgCustom = obj.topSvgCustom;
          this.currentThemeClass = obj.sideTheme; // sidebar主题类名
        }
      }
    },
    // 设置系统主题Svg颜色属性
    setLeftColor(list) {
      list.forEach((obj, i) => {
        if (obj.sideTheme == "theme-theme") {
          obj.leftColorActive = this.formatColor_deep(obj.theme, 80);
          obj.leftColor = obj.theme;
        } else if (
          obj.sideTheme == "theme-dark" ||
          obj.sideTheme == "theme-light"
        ) {
          obj.leftColorActive = obj.theme;
          obj.leftColor = obj.sideTheme == "theme-dark" ? "#303648" : "#ffffff";
        } else if (obj.sideTheme == "theme-custom") {
          obj.leftColorActive = obj.subMenuBackgroundActiveCustom;
          obj.leftColor = obj.menuBackgroundCustom;
        } else {
          obj.leftColorActive = "#fff";
          obj.leftColor = "#fff";
        }
        obj.topColor = obj.topBackgroundCustom || "#fff";
      });
    },
    // 保存文件片段到后台
    async saveFileChunk(file, chunkList, fileMd5, fileName, chunkSize) {
      console.log(file, chunkList, fileMd5, fileName, chunkSize);
      let percentage = (100 / chunkList.length).toFixed(2);
      let uid = getUploadUid();
      this.addLoading = true;
      for (let i = 0; i < chunkList.length; i++) {
        let formData = new FormData();
        formData.append("id", file.uid);
        formData.append("chunk", i); // 当前片段的索引
        formData.append("size", chunkSize); // 切片的文件分片大小 (就是以多少字节进行分片的，这里是5M)
        formData.append("chunks", chunkList.length); // 共有多少分片
        formData.append("file", chunkList[i]); // 当前分片的文件流
        formData.append("md5", fileMd5); // 整个文件的MD5唯一标识码，不是分片
        formData.append("name", fileName); // 文件的名称
        formData.append("chunkSize", chunkList[i].size); // 当前切片的大小（最后一片不一定是5M）
        formData.append("uid", uid);
        formData.append("lastModifiedDate", file.lastModifiedDate); // 修改时间
        try {
          const data = await importThemeFile(formData);
          if (i == chunkList.length - 1) {
            this.addLoading = false;
            this.eventClose();
            this.successMsg(this.$t("msg.add"));
            await this.initThemeAll();
          }
          // 新添加的主题带着new上标
          //   this.themeAll[this.themeAll.length - 1].isNew = true;
        } catch (error) {
          this.addLoading = false;
          break;
        }
      }
    },
    // 增加系统主题
    async addTheme(file2, type) {
      if (type) {
        let file = file2;
        let fileMd5 = null;
        try {
          fileMd5 = await getFileMd5(file);
        } catch (e) {}
        if (!fileMd5) return;
        // 每片的大小为 5M 可调整
        const chunkSize = 5 * 1024 * 1024;
        // 文件分片储存
        let chunkList = [];
        let chunkPush = (page = 1) => {
          chunkList.push(file.slice((page - 1) * chunkSize, page * chunkSize));
          if (page * chunkSize < file.size) {
            chunkPush(page + 1);
          }
        };
        chunkPush();
        // 接口分片存储.zip
        this.saveFileChunk(file, chunkList, fileMd5, file.name, chunkSize);
      } else {
        // theme-others本地存储.json
        this.themeOthers.push(file);
        localStorage.setItem("theme-others", JSON.stringify(this.themeOthers));
        this.initThemeAll();
        // 新添加的主题带着new上标
        this.themeAll[this.themeAll.length - 1].isNew = true;
      }
    },
    // 自定义主题存为系统主题
    addThemeNameAndSave(form) {
      this.isCustom = false;
      this.themeNameZh = form.themeNameZh;
      this.themeNameEn = form.themeNameEn;
      this.currentThemeObj_sys = JSON.parse(
        JSON.stringify(this.currentThemeObj),
      );
      this.addTheme(this.currentThemeObj, this.addType);
    },
    // 删除系统主题
    async deleteTheme(data, type) {
      if (type) {
        // 接口
        try {
          this.loading = true;

          await deleteTheme({ themeId: data.themeId });
          this.successMsg(this.$t("msg.delete"));
          // 如果恰好删除了本地主题,则样式需要保留,但命名改为自定义主题
          let layoutData = JSON.parse(
            localStorage.getItem("layout-setting") || "{}",
          );
          let layoutId = layoutData.themeId || "";
          if (layoutId == data.themeId) {
            this.themeNameZh = "自定义主题";
            this.themeNameEn = "Custom Topic";
            this.currentThemeId = "000";
            // 本地存储
            this.$cache.local.set(
              "layout-setting",
              `{
                 "tagsView":${layoutData.tagsView},
                 "webTitle":${this.webTitle},
                 "sidebarLogo":${layoutData.sidebarLogo},
                 "dynamicTitle":${layoutData.dynamicTitle},
                 "sideTheme":"${layoutData.sideTheme}",
                 "themeNameZh":"${this.themeNameZh}",
                 "themeNameEn":"${this.themeNameEn}",
                 "theme":"${layoutData.theme}",
                 "themeId":"${this.currentThemeId}",
                 "menuColorCustom":"${layoutData.menuColorCustom}",
                 "menuBackgroundCustom":"${layoutData.menuBackgroundCustom}",
                 "menuColorActiveCustom":"${layoutData.menuColorActiveCustom}",
                 "subMenuBackgroundActiveCustom":"${layoutData.subMenuBackgroundActiveCustom}",
                 "subMenuBackgroundCustom":"${layoutData.subMenuBackgroundCustom}",
                 "subMenuHoverCustom":"${layoutData.subMenuHoverCustom}",
                 "topBackgroundCustom":"${layoutData.topBackgroundCustom}",
                 "topSvgCustom":"${layoutData.topSvgCustom}"
               }`,
            );
          }

          await this.initThemeAll();
          this.loading = false;
        } catch (err) {
          this.loading = false;
        }
      } else {
        // 本地
        this.themeOthers = this.themeOthers.filter((obj) => {
          return obj.themeNameZh != data.themeNameZh;
        });
        localStorage.setItem("theme-others", JSON.stringify(this.themeOthers));
        this.initThemeAll();
        // 如果恰好删除了当前主题
        if (this.currentThemeId == data.themeId) {
          this.handleTheme(this.themeAll[0]);
        }
        // 如果恰好删除了本地主题
        let layoutData = JSON.parse(
          localStorage.getItem("layout-setting") || "{}",
        );
        let layoutId = layoutData.themeId || "";
        if (layoutId == data.themeId) {
          this.$cache.local.remove("layout-setting");
        }
      }
    },
    // 修改主题颜色
    themeChange(val) {
      this.$store.dispatch("settings/changeSetting", {
        key: "theme",
        value: val,
      });
      this.theme = val;
    },
    // 修改vuex和this属性
    attrChange(attr, val, obj) {
      if (this[attr] !== undefined) {
        this.$store.dispatch("settings/changeSetting", {
          key: attr,
          value: val,
        });
        this[attr] = val;
        // 如果传入obj,则同步修改obj属性
        if (obj) {
          obj[attr] = val;
        }
      }
    },
    // 每次修改sideTheme和theme都需重新设置属性,第三个参数只有切换系统主题时传入
    initAttr(sideTheme, theme, obj) {
      this.currentThemeClass = sideTheme;
      // 如果不是theme-custom,需修改sidebar
      if (sideTheme != "theme-custom") {
        // 如果是theme-theme,则自动勾选跟随主题颜色
        if (sideTheme == "theme-theme") {
          // 手动修改sidebar属性
          this.attrChange("menuColorCustom", "#fff", obj);
          this.attrChange("menuBackgroundCustom", theme, obj);
          this.attrChange("menuColorActiveCustom", "#fff", obj);
          this.attrChange(
            "subMenuBackgroundCustom",
            this.formatColor_deep(theme, 50),
            obj,
          );
          this.attrChange(
            "subMenuBackgroundActiveCustom",
            this.formatColor_deep(theme, 80),
            obj,
          );
          this.attrChange(
            "subMenuHoverCustom",
            this.formatColor_deep(theme, 80),
            obj,
          );
        } else if (sideTheme == "theme-light") {
          this.attrChange("menuColorCustom", "#000", obj);
          this.attrChange("menuBackgroundCustom", "#fff", obj);
          this.attrChange("menuColorActiveCustom", "#fff", obj);
          this.attrChange("subMenuBackgroundCustom", theme + "19", obj);
          this.attrChange("subMenuBackgroundActiveCustom", theme, obj);
          this.attrChange("subMenuHoverCustom", theme + "29", obj);
        } else if (sideTheme == "theme-dark") {
          this.attrChange("menuColorCustom", "#fff", obj);
          this.attrChange("menuBackgroundCustom", "#1C2439", obj);
          this.attrChange("menuColorActiveCustom", "#fff", obj);
          this.attrChange("subMenuBackgroundCustom", "#000", obj);
          this.attrChange("subMenuBackgroundActiveCustom", theme, obj);
          this.attrChange("subMenuHoverCustom", "rgba(0, 0, 0, 0.3)", obj);
        }
      }
    },
    // 更改系统主题
    async handleTheme(obj) {
      this.attrChange("isCustom", false);
      this.currentThemeId = obj.themeId;
      this.currentThemeObj_sys = JSON.parse(JSON.stringify(obj));

      // 设置每一项
      Object.keys(this.currentThemeObj_sys).forEach((key) => {
        this.attrChange(key, this.currentThemeObj_sys[key]);
      });
      // 第三个参数传入obj,修改obj
      this.initAttr(
        this.currentThemeObj_sys.sideTheme,
        this.currentThemeObj_sys.theme,
        this.currentThemeObj_sys,
      );
      await this.getSysImgById();
    },
    // 自定义颜色
    setColor(colorName, color, isAddClass) {
      if (isAddClass) {
        // 侧边栏自定义类名theme-custom
        this.$store.dispatch("settings/changeSetting", {
          key: "sideTheme",
          value: "theme-custom",
        });
        // 当前
        this.sideTheme = "theme-custom";
      }
      // 颜色vuex暂时存储
      this.$store.dispatch("settings/changeSetting", {
        key: colorName,
        value: color,
      });
      // 改变根元素css变量
      const root = document.querySelector(":root");
      root.style.setProperty(this.rootCssList[colorName], color);
      // 当前
      this[colorName] = color;
    },
    // 自定义Logo
    setLogo(name, val) {
      this.$store.dispatch("settings/changeSetting", {
        key: name,
        value: val,
      });
      this[name] = val;
    },
    // 自定义图片
    setImage(name, url, blob) {
      // this.imageUrl = url;
    },
    resetLogoSetting() {},
    // 保存
    async saveSetting() {
      this.confirmMessage(this.$t("tips.isCoverSystemTip"))
        .then(() => {
          this.isCoverSystem = true;
          this.localSave();
        })
        .catch(() => {});
    },
    localSave() {
      this.$modal.loading(this.$t("tips.saveThemeConfigLoading"));
      this.currentThemeObj_copy = JSON.parse(
        JSON.stringify(this.currentThemeObj),
      );
      // 本地存储
      this.$cache.local.set(
        "layout-setting",
        `{
            "tagsView":${this.tagsView},
            "webTitle":"${this.webTitle}",
            "sidebarLogo":${this.sidebarLogo},
            "dynamicTitle":${this.dynamicTitle},
            "sideTheme":"${this.sideTheme}",
            "themeNameZh":"${this.themeNameZh}",
            "themeNameEn":"${this.themeNameEn}",
            "theme":"${this.theme}",
            "themeId":"${this.currentThemeId}",
            "menuColorCustom":"${this.menuColorCustom}",
            "menuBackgroundCustom":"${this.menuBackgroundCustom}",
            "menuColorActiveCustom":"${this.menuColorActiveCustom}",
            "subMenuBackgroundActiveCustom":"${this.subMenuBackgroundActiveCustom}",
            "subMenuBackgroundCustom":"${this.subMenuBackgroundCustom}",
            "subMenuHoverCustom":"${this.subMenuHoverCustom}",
            "topBackgroundCustom":"${this.topBackgroundCustom}",
            "topSvgCustom":"${this.topSvgCustom}"
          }`,
      );
      this.$cache.local.set("isCoverSystem", this.isCoverSystem);
      setTimeout(() => {
        this.$modal.closeLoading();
      }, 2000);
    },
    // 重置
    resetSetting() {
      this.confirmMessage(this.$t("tips.resetThemeConfigTip"))
        .then(() => {
          this.$modal.loading(this.$t("tips.removeLoading"));
          setTimeout("window.location.reload()", 1000);
        })
        .catch(() => {});
    },

    // 导出当前主题
    exportSetting() {
      this.confirmMessage(this.$t("tips.exportThemeConfigTip"))
        .then(() => {
          let obj = JSON.parse(JSON.stringify(this.currentThemeObj));
          if (this.isCustom) {
            let d = +new Date();
            obj.themeNameZh = "自定义主题 " + d;
            obj.themeNameEn = "Custom Topic " + d;
          }
          const codeStr = beautifier(JSON.stringify(obj), {
            indent_size: 2,
            space_in_empty_paren: true,
          });
          const blob = new Blob([codeStr], {
            type: "text/plain;charset=utf-8",
          });
          this.$download.saveAs(blob, "theme.json");
        })
        .catch(() => {});
    },
    openDig(type) {
      this[type] = true;
    },
    eventClose() {
      this.toSysDigVisible = false;
      this.dialogVisible = false;
      this.configDig = false;
    },
  },
  inject: ["getDefaultTheme"],
  async beforeRouteLeave(to, from, next) {
    this.getDefaultTheme(false);
    next();
  },
};
</script>

<style lang="scss" scoped>
.themePanel-container {
  width: 100%;
  height: 100%;
  // overflow: auto;
  .themePanel-container-part {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0px 0px 10px 4px var(--color-primary19);
  }
  .themePanel-scroll {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}

.drawer-footer {
  width: 100%;
  padding: 20px;
  .drawer-item {
    width: 300px;
    display: flex;
    justify-content: space-between;
  }
  .search-btn {
    width: 120px;
    height: 32px;
    padding: 0;
    line-height: 32px;
    font-size: 14px;
  }
}
.drawer-container {
  padding: 20px;
  font-size: 14px;
  line-height: 1.5;
  .setting-drawer-content {
    .setting-drawer-head {
      position: relative;
      .search-btn {
        width: 120px;
        height: 32px;
        padding: 0;
        line-height: 32px;
        font-size: 14px;
      }
      .drawer-head {
        font-size: 18px;
      }
      .drawer-head-btns {
        position: absolute;
        top: -5px;
        right: 0px;
      }
    }
    .setting-drawer-title {
      .drawer-title {
        margin: 15px 0;
        color: rgba(0, 0, 0, 0.85);
        font-size: 16px;
      }
    }
  }
  .drawer-item {
    width: 300px;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    padding: 12px 0;
    // height: 70px;
    margin-left: 12px;
    .drawer-item-title {
      font-size: 16px;
      color: var(--color-primary);
      margin-bottom: 15px;
    }
    .drawer-item-checkbox {
      padding: 12px 0;
    }
    .drawer-item-theme-color {
      display: block;
      height: 60px;
    }
  }

  .drawer-switch {
    float: right;
  }
}
</style>
<style lang="scss">
.themePanel-container {
  .drawer-container {
    .el-radio-group {
      display: flex;
      .el-radio {
        flex: 1;
      }
    }
  }
}
</style>
