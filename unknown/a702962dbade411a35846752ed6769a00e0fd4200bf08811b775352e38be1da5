<template>
  <div class="full-height parentDiv">
    <i-frame
      :src="url"
      style="height: 100%"
      id="bdIframe"
      :class="isFixed ? 'fixed-iframe' : ''"
    />
    <el-dialog
      :visible.sync="dialogTableVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    ></el-dialog>
  </div>
</template>
<script>
import iFrame from "@/components/iFrame/index";
export default {
  name: "IframeCom",
  components: { iFrame },
  data() {
    return {
      dialogTableVisible: false,
      isFixed: false,
    };
  },
  computed: {
    url() {
      let { url } = this.$route.query;
      return window.location.origin + decodeURIComponent(url);
    },
  },
  methods: {
    dialogInit(val) {
      this.dialogTableVisible = val;
      if (val) {
        //当子页面弹框开时
        //父页面外部滚动条隐藏
        // document.getElementsByClassName("indexCon")[0].style.overflow = "hidden";
        //将子页面iframe页面层级提升
        console.log(document.getElementById("parentDiv"));
        document.getElementById("parentDiv").style.position = "relative";
        document.getElementById("bdIframe").style.position = "absolute";
        document.getElementById("bdIframe").style.top = 0;
        document.getElementById("bdIframe").style.left = 0;
        document.getElementById("bdIframe").style.right = 0;
        document.getElementById("bdIframe").style.bottom = 0;
        document.getElementById("bdIframe").style.zIndex = 10000;
      } else {
        //当子页面弹框关时
        //恢复
        // document.getElementsByClassName("indexCon")[0].style.overflow = "auto";
        document.getElementById("parentDiv").style.position = "unset";
        document.getElementById("bdIframe").style.position = "unset";
        document.getElementById("bdIframe").style.zIndex = "unset";
      }
    },
    fullIframeFixed(val) {
      this.isFixed = val.fullFixed;
    },
  },
  mounted() {
    // 子项目触发弹窗
    window.$eventBus.$on("dialog", this.dialogInit);
    window.$eventBus.$on("fullIframeFixed", this.fullIframeFixed);
    // 中英文切换触发给子项目
    // this.pushChildInfo()
  },
};
</script>
<style lang="scss" scoped>
.parentDiv {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
}

.fixed-iframe {
  position: fixed;
  left: 0px;
  right: 0px;
  top: 0px;
  background: hsla(0, 0%, 100%, 0.9);
  z-index: 9999;
  opacity: 0.4;
}
</style>
