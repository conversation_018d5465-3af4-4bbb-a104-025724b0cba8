<template>
  <!-- 图片裁切区 -->
  <el-dialog
    :title="$t('common.cropper')"
    :visible.sync="cropperModel"
    width="900px"
    :before-close="onCancelClick"
    :close-on-click-modal="false"
    @open="onOpen"
  >
    <div class="dig-content cropper-content">
      <div class="cropper-box">
        <div class="cropper">
          <div v-if="chooseP" class="choose-plz">
            <div class="chhose-plz-text">
              {{ $t("common.cropperClick") }}
            </div>
          </div>
          <vue-cropper
            ref="cropper"
            class="cropper-11111"
            style="
              background-image: none;
              background-color: rgba(31, 45, 61, 0.8);
            "
            :img="option.img"
            :output-size="option.outputSize"
            :output-type="option.outputType"
            :info="option.info"
            :can-scale="option.canScale"
            :auto-crop="option.autoCrop"
            :fixed="option.fixed"
            :fixed-number="option.fixedNumber"
            :full="option.full"
            :fixed-box="option.fixedBox"
            :can-move="option.canMove"
            :can-move-box="option.canMoveBox"
            :original="option.original"
            :center-box="option.centerBox"
            :height="option.height"
            :info-true="option.infoTrue"
            :max-img-size="option.maxImgSize"
            :enlarge="option.enlarge"
            :mode="option.mode"
            @realTime="realTime"
            @imgLoad="imgLoad"
          />
        </div>
      </div>
      <!--预览效果图-->
      <div class="show-preview">
        <div :style="previews.div" class="preview">
          <img :src="previews.url" :style="previews.img" />
        </div>
      </div>
    </div>
    <!--底部操作工具按钮-->
    <div class="cropper-footer-btn">
      <div class="scope-btn" @click="chooseP = false">
        <!-- 选择图片 -->
        <el-button
          size="small"
          class="search-btn btn"
          type="primary"
          @click="clickUpload"
          >{{ $t("common.choosePicture") }}</el-button
        >
        <input
          id="uploads"
          ref="uploads"
          type="file"
          style="position: absolute; clip: rect(0 0 0 0)"
          accept="image/png, image/jpeg, image/gif, image/jpg"
          @change="selectImg($event)"
        />
        <el-button
          size="small"
          class="search-btn"
          icon="el-icon-zoom-in"
          @click="changeScale(1)"
        ></el-button>
        <el-button
          size="small"
          class="search-btn"
          icon="el-icon-zoom-out"
          @click="changeScale(-1)"
        ></el-button>
        <el-button
          size="small"
          class="search-btn"
          icon="el-icon-refresh-left"
          @click="rotateLeft"
        ></el-button>
        <el-button
          size="small"
          class="search-btn"
          icon="el-icon-refresh-right"
          @click="rotateRight"
        ></el-button>
      </div>
      <!-- 提交 -->
      <div class="upload-btn">
        <el-button
          size="small"
          class="search-btn"
          type="primary"
          @click="uploadImg('blob')"
          >{{ $t("common.submit") }}</el-button
        >
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { VueCropper } from "vue-cropper";

export default {
  components: { VueCropper },
  data() {
    return {
      uploading: false,
      chooseP: false,
      logoFileList: [],
      // 图片裁切
      formValidate: {
        mainImage: "",
      },
      ruleValidate: {
        mainImage: [
          {
            required: true,
            message: this.$t("common.uploadPicture"),
            trigger: "blur",
          },
        ],
      },
      blob: null, // 保存图片blob
      // 裁切图片参数
      title: "",
      imgName: "",
      imgVisible: false,
      src: "",
      loadingSkeleton: false,
      // 裁切组件
      previews: {},
      option: {
        img: "", // 裁剪图片的地址
        outputSize: 1, // 裁剪生成图片的质量(可选0.1 - 1)
        outputType: "png", // 裁剪生成图片的格式（jpeg || png || webp）
        info: false, // 图片大小信息
        canScale: true, // 图片是否允许滚轮缩放
        autoCrop: true, // 是否默认生成截图框
        fixed: true, // 是否开启截图框宽高固定比例
        fixedNumber: [2, 1], // 截图框的宽高比例
        full: false, // false按原比例裁切图片，不失真
        fixedBox: false, // 固定截图框大小，不允许改变
        canMove: false, // 上传图片是否可以移动
        canMoveBox: true, // 截图框能否拖动
        original: false, // 上传图片按照原始比例渲染
        centerBox: false, // 截图框是否被限制在图片里面
        height: true, // 是否按照设备的dpr 输出等比例图片
        infoTrue: false, // true为展示真实输出图片宽高，false展示看到的截图框宽高
        maxImgSize: 3000, // 限制图片最大宽度和高度
        enlarge: 1, // 图片根据截图框输出比例倍数
        mode: "230px 150px", // 图片默认渲染方式
      },
    };
  },
  props: {
    cropperModel: {
      default: false,
      type: Boolean,
    },
    cropperWidth: {
      default: 300,
      type: Number,
    },
    cropperHeight: {
      default: 300,
      type: Number,
    },
    name: {
      default: "",
      type: String,
    },
  },
  created() {},
  methods: {
    onOpen() {
      this.chooseP = true;
      this.option.fixedNumber = [this.cropperWidth, this.cropperHeight]; // 重置截图框宽高比
    },
    onCancelClick() {
      this.$emit("eventClose");
    },
    clickUpload() {
      this.$refs.uploads.click();
    },
    // 图片加载的回调
    imgLoad() {},
    changeScale(num) {
      num = num || 1;
      this.$refs.cropper.changeScale(num);
    },
    rotateLeft() {
      this.$refs.cropper.rotateLeft();
    },
    rotateRight() {
      this.$refs.cropper.rotateRight();
    },
    // 实时预览
    realTime(data) {
      this.previews = data;
    },
    // 选择图片
    selectImg(e) {
      const file = e.target.files[0];
      if (!/\.(jpg|jpeg|png|JPG|PNG)$/.test(e.target.value)) {
        this.$message({
          message: this.$t("common.pictureType"),
          type: "error",
        });
        return false;
      }
      // 转化为blob
      const reader = new FileReader();
      reader.onload = (e) => {
        let data;
        if (typeof e.target.result === "object") {
          data = window.URL.createObjectURL(new Blob([e.target.result]));
        } else {
          data = e.target.result;
        }
        this.option.img = data;
      };
      // 转化为base64
      reader.readAsDataURL(file);
    },
    // 确定图片
    uploadImg(type) {
      if (type === "blob") {
        // 获取截图的blob数据
        this.$refs.cropper.getCropBlob(async (data) => {
          let url = URL.createObjectURL(data);
          let blob = data;
          this.$emit("setImage", this.name, url, blob);
          this.onCancelClick();
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.upload-btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  box-shadow: 0 0 1px #cccccc;
}
.upload-btn i {
  margin: 5px;
}

.cropper-content {
  display: flex;
  display: -webkit-flex;
  justify-content: flex-end;
  .cropper-box {
    flex: 1;
    width: 100%;
    // 必须设置宽高
    .cropper {
      width: 430px;
      height: 300px;
    }
  }
  .show-preview {
    flex: 1;
    -webkit-flex: 1;
    display: flex;
    display: -webkit-flex;
    justify-content: center;
    .preview {
      overflow: hidden;
      border: 1px solid #bbb;
      background: #cccccc;
    }
  }
}

.cropper-footer-btn {
  margin-top: 30px;
  display: flex;
  display: -webkit-flex;
  justify-content: space-between;
  .scope-btn {
    width: 430px;
    display: flex;
    display: -webkit-flex;
    justify-content: flex-start;
  }
  .btn {
    margin-right: 10px;
  }
}
.cropper {
  position: relative;
}
.cropper .choose-plz {
  position: absolute;
  top: 74px;
  left: 132px;
  z-index: 2;
  border: 1px dashed #fff;
  width: 150px;
  height: 150px;
  line-height: 150px;
  text-align: center;
  // padding: 52px 30px;
  font-size: 14px;
  text-align: center;
  color: #fff;
  border-radius: 6px;
  user-select: none;
  transition: all 2s;
  opacity: 1;
}
.cropper-box:hover .choose-plz {
  // z-index:0;
  opacity: 0;
}
</style>
<style lang="scss">
.cropper-footer-btn {
  .search-btn {
    // width: 72px;
    height: 32px;
    padding: 0 15px;
    line-height: 30px;
    font-size: 14px;
  }
}
</style>
