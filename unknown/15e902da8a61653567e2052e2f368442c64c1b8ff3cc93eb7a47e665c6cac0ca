import SearchTableContent from "./searchTableContent/index";
import SearchListContent from "./searchListContent/index";
import SearchItem from "./searchItem/index";
import commonHeader from "./commonHeader/index";
import contentDetail from "./contentDetail/index";
import Operatebtns from "./operatebtns/index";
import moreBtns from "./moreBtns/index";
import actionBtns from "./actionBtns/index";
import SwFormItem from "./SwFormItem/index";
import DictTag from "./DictTag/index";
import basicTable from "./basicTable/index";
import dialogForm from "./dialogForm/index";
import IpInput from "./IpInput/index";
import PaginationSelect from "./PaginationSelect/index";
import virtualList from "vue-virtual-scroll-list";
import pageHeader from "./pageHeader/index";
import pageFlow from "./pageFlow/index";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import vueEcharts from "./vueEcharts/ECharts.vue";
import noData from "./vueEcharts/noData.vue";
import SwNodata from "./SwNodata/index";
import alertTips from "./baseTips/alert.vue";
import textTips from "./baseTips/text.vue";
import baseTitle from "./baseTitle/index";
import singleFileUpload from "./singleFileUpload/index";
const install = (Vue) => {
  Vue.component("search-table-content", SearchTableContent);
  Vue.component("search-list-content", SearchListContent);
  Vue.component("search-item", SearchItem);
  Vue.component("common-header", commonHeader);
  Vue.component("content-detail", contentDetail);
  Vue.component("Operatebtns", Operatebtns);
  Vue.component("moreBtns", moreBtns);
  Vue.component("action-btns", actionBtns);
  Vue.component("sw-form-item", SwFormItem);
  Vue.component("sw-nodata", SwNodata);
  Vue.component("dict-tag", DictTag);
  Vue.component("basic-table", basicTable);
  Vue.component("dialog-form", dialogForm);
  Vue.component("treeselect", Treeselect);
  Vue.component("ip-input", IpInput);
  Vue.component("pagination-select", PaginationSelect);
  Vue.component("virtual-list", virtualList);
  Vue.component("page-header", pageHeader);
  Vue.component("page-flow", pageFlow);
  Vue.component("vue-echarts", vueEcharts);
  Vue.component("no-data", noData);
  Vue.component("alert-tips", alertTips);
  Vue.component("text-tips", textTips);
  Vue.component("base-title", baseTitle);
  Vue.component("single-file-upload", singleFileUpload);
};
export default install;
