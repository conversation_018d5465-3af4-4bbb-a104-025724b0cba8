<template>
  <span>
    <el-tag
      :disable-transitions="true"
      v-bind="getStyle"
      size="mini"
      effect="plain"
    >
      <span
        class="el-icon-loading"
        v-show="checkLoading"
        style="font-size: 14px"
      ></span>
      <slot name="title"></slot>
      <slot name="content"></slot>
    </el-tag>
  </span>
</template>

<script>
export default {
  name: "DictTagCom",
  props: {
    options: {
      type: Array,
      default: null,
    },
    labelName: {
      type: String,
      default: "label",
    },
    valueName: {
      type: String,
      default: "value",
    },
    value: [Number, String, Array],
    styles: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  computed: {
    getStyle() {
      let item = this.options.find((key) => key[this.valueName] == this.value);
      if (!item) {
        return {};
      }
      if (item.listClass) {
        return { type: item.listClass, class: item.cssClass || "" };
      } else {
        return {
          type: this.styles[item[this.valueName]]?.listClass || "",
          class: "",
        };
      }
    },
    checkLoading() {
      let item = this.options.find((key) => key[this.valueName] == this.value);
      if (!item) {
        return false;
      }
      if (item.loading == true) {
        return true;
      }
      if (this.styles[item[this.valueName]]?.loading == true) {
        return true;
      }
      return false;
    },
  },
};
</script>
<style scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}
</style>
