import Vue from "vue";
import VueI18n from "vue-i18n";
import elementEnLocale from "element-ui/lib/locale/lang/en";
import elementZhLocale from "element-ui/lib/locale/lang/zh-CN";
import enLocale from "./en.json";
import zhLocale from "./zh.json";
import customEn from "./customize/en.json";
import customZh from "./customize/zh.json";
import { getLanguage } from "@/utils/language";
import { mergeRecursive } from "@/utils/util";
Vue.use(VueI18n);
var zh = mergeRecursive(zhLocale, customZh);
var en = mergeRecursive(enLocale, customEn);
const messages = {
  en: {
    ...elementEnLocale,
    ...en,
  },
  zh: {
    ...elementZhLocale,
    ...zh,
  },
};
export function getLang() {
  const chooseLanguage = getLanguage();
  if (chooseLanguage) return chooseLanguage;

  // if has not choose language
  const language = (
    navigator.language || navigator.browserLanguage
  ).toLowerCase();
  const locales = Object.keys(messages);
  for (const locale of locales) {
    if (language.indexOf(locale) > -1) {
      return locale;
    }
  }
  return "zh";
}
const i18n = new VueI18n({
  // set locale
  // options: en | zh | es
  locale: getLang(),
  // set locale messages
  messages,
});

export default i18n;
