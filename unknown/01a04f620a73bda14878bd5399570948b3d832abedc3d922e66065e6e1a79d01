@font-face {
  font-family: "Montserrat";
  src: url("../font/Montserrat/Montserrat-Thin.woff2");
  font-weight: 100;
  font-style: normal;
}
@font-face {
  font-family: "Montserrat";
  src: url("../font/Montserrat/Montserrat-ExtraLight.woff2");
  font-weight: 200;
  font-style: normal;
}
@font-face {
  font-family: "Montserrat";
  src: url("../font/Montserrat/Montserrat-Light.woff2");
  font-weight: 300;
  font-style: normal;
}
@font-face {
  font-family: "Montserrat";
  src: url("../font/Montserrat/Montserrat-Medium.woff2");
  font-weight: 500;
  font-style: normal;
}
@font-face {
  font-family: "Montserrat";
  src: url("../font/Montserrat/Montserrat-SemiBold.woff2");
  font-weight: 600;
  font-style: normal;
}
@font-face {
  font-family: "Montserrat";
  src: url("../font/Montserrat/Montserrat-Bold.woff2");
  font-weight: 700;
  font-style: normal;
}
@font-face {
  font-family: "Montserrat";
  src: url("../font/Montserrat/Montserrat-ExtraBold.woff2");
  font-weight: 800;
  font-style: normal;
}
@font-face {
  font-family: "Montserrat";
  src: url("../font/Montserrat/Montserrat-Black.woff2");
  font-weight: 900;
  font-style: normal;
}
@font-face {
  font-family: "Montserrat";
  src: url("../font/Montserrat/Montserrat-Regular.woff2");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: "SourceHanSansCN";
  src: url("../font/SourceHanSansCN/SourceHanSansCN-VF.ttf.woff2");
  font-display: swap;
}
