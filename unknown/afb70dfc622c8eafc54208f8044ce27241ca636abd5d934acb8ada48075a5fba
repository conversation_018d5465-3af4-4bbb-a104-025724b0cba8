"use strict";

/**
 * 杂凑算法类型
 */
var SGD_HashType;
(function (SGD_HashType) {
  SGD_HashType[(SGD_HashType["SM3"] = 1)] = "SM3";
  SGD_HashType[(SGD_HashType["SHA1"] = 2)] = "SHA1";
  SGD_HashType[(SGD_HashType["SHA256"] = 4)] = "SHA256";
})(SGD_HashType || (SGD_HashType = {}));
/**
 * 对称算法
 */
var SGD_SymmType;
(function (SGD_SymmType) {
  SGD_SymmType[(SGD_SymmType["SM1_ECB"] = 257)] = "SM1_ECB";
  SGD_SymmType[(SGD_SymmType["SM1_CBC"] = 258)] = "SM1_CBC";
  SGD_SymmType[(SGD_SymmType["SSF33_ECB"] = 33)] = "SSF33_ECB";
  SGD_SymmType[(SGD_SymmType["SSF33_CBC"] = 34)] = "SSF33_CBC";
  SGD_SymmType[(SGD_SymmType["SM4_ECB"] = 1025)] = "SM4_ECB";
  SGD_SymmType[(SGD_SymmType["SM4_CBC"] = 1026)] = "SM4_CBC";
})(SGD_SymmType || (SGD_SymmType = {}));
/**
 * 填充方式
 */
var SGD_PaddingType;
(function (SGD_PaddingType) {
  SGD_PaddingType[(SGD_PaddingType["NoPad"] = 0)] = "NoPad";
  SGD_PaddingType[(SGD_PaddingType["PKCS_5"] = 1)] = "PKCS_5";
})(SGD_PaddingType || (SGD_PaddingType = {}));
/**
 * 口令类型
 */
var SGD_UserPinType;
(function (SGD_UserPinType) {
  SGD_UserPinType[(SGD_UserPinType["AdminType"] = 0)] = "AdminType";
  SGD_UserPinType[(SGD_UserPinType["UserType"] = 1)] = "UserType";
})(SGD_UserPinType || (SGD_UserPinType = {}));
/**
 * 标准SKF接口对象
 */
var SKF = /** @class */ (function () {
  /**
   * @param libName 动态库名称
   * @param port 组件服务端口号，默认为9876
   */
  function SKF(libName, port, srvUrl) {
    this._libName = libName;
    this._xhr = new XMLHttpRequest();
    if (port == undefined || port == null || port + "" == "") {
      this._port = 9876;
    } else {
      this._port = port;
    }
    if (srvUrl != null && srvUrl != undefined && srvUrl != "") {
      this._srvUrl = srvUrl;
    } else {
      this._srvUrl = "https://localhost:" + this._port + "/skf/";
    }
  }
  /**
   * 设置SKF接口库名称
   * @param libName skf接口库名称
   */
  SKF.prototype.setLibName = function (libName) {
    this._libName = libName;
  };
  /**
   * 调用服务
   * @param method 方法名
   * @param data
   */
  SKF.prototype.postInvokeSrv = function (method, data) {
    this._xhr.open("POST", this._srvUrl + method, false);
    this._xhr.setRequestHeader(
      "Content-Type",
      "application/json; charset=utf-8",
    );
    this._xhr.send(data);
    if (this._xhr.status == 200) {
      return JSON.parse(this._xhr.responseText);
    } else {
      return {
        status: this._xhr.status,
        msg: this._xhr.statusText,
      };
    }
  };
  /**
   * 获取模块版本号
   * @returns 版本号
   */
  SKF.prototype.GetVersion = function () {
    var rv = this.postInvokeSrv("GetVersion", null);
    if (rv.status == 0) {
      return JSON.parse(this._xhr.responseText).value;
    } else {
      return "";
    }
  };
  /**
   * 异步获取模块版本号
   * @param callback
   */
  SKF.prototype.GetVersionAsync = function (callback) {
    var xhr = new XMLHttpRequest();
    xhr.open("POST", this._srvUrl + "GetVersion", true);
    xhr.setRequestHeader("Content-Type", "application/json; charset=utf-8");
    xhr.timeout = 1000;
    xhr.onreadystatechange = function () {
      if (xhr.readyState === 4) {
        if (xhr.status === 200) {
          callback(JSON.parse(xhr.responseText).value);
        } else {
          callback("");
        }
      }
    };
    xhr.send(null);
  };
  /**
   * 设置日志等级
   * @param level 日志等级 0--6
   * @returns
   */
  SKF.prototype.SetLogLevel = function (level) {
    return this.postInvokeSrv(
      "SetLogLevel",
      JSON.stringify({
        level: level,
      }),
    );
  };
  /**
   * 枚举设备
   * @returns,设备名称列表
   */
  SKF.prototype.SKF_EnumDev = function () {
    if (this._libName == null || this._libName == undefined)
      throw new Error("this libName is null or empty");
    return this.postInvokeSrv(
      "SKF_EnumDev",
      JSON.stringify({
        skfLibFn: this._libName,
      }),
    );
  };
  /**
   * 连接指定的设备
   * @param devName 设备名称
   */
  SKF.prototype.SKF_ConnectDev = function (devName) {
    if (this._libName == null || this._libName == undefined)
      throw new Error("this libName is null or empty");
    return this.postInvokeSrv(
      "SKF_ConnectDev",
      JSON.stringify({
        skfLibFn: this._libName,
        devName: devName,
      }),
    );
  };
  /**
   * 断开指定的设备
   * @param hDev 设备句柄
   */
  SKF.prototype.SKF_DisConnectDev = function (hDev) {
    return this.postInvokeSrv(
      "SKF_DisConnectDev",
      JSON.stringify({
        hDev: hDev,
      }),
    );
  };
  /**
   * 设置设备标签
   * @param hDev 设备句柄
   * @param label 设备标签
   */
  SKF.prototype.SKF_SetLabel = function (hDev, label) {
    return this.postInvokeSrv(
      "SKF_SetLabel",
      JSON.stringify({
        hDev: hDev,
        label: label,
      }),
    );
  };
  /**
   * 获取设备标签
   * @param hDev 设备句柄
   * @returns GM0016 struct DevInfo
   */
  SKF.prototype.SKF_GetDevInfo = function (hDev) {
    return this.postInvokeSrv(
      "SKF_GetDevInfo",
      JSON.stringify({
        hDev: hDev,
      }),
    );
  };
  /**
   * 修改UkeyPin
   * @param hDev 设备句柄
   * @param oldPin 旧口令
   * @param newPin 新口令
   * @param pinType 口令类型
   */
  SKF.prototype.SKF_ChangePIN = function (hDev, oldPin, newPin, pinType) {
    return this.postInvokeSrv(
      "SKF_ChangePIN",
      JSON.stringify({
        hDev: hDev,
        oldPin: oldPin,
        newPin: newPin,
        pinType: pinType,
      }),
    );
  };
  /**
   * 获取PIN信息
   * @param hDev 设备句柄
   * @param pinType 口令类型
   */
  SKF.prototype.SKF_GetPINInfo = function (hDev, pinType) {
    return this.postInvokeSrv(
      "SKF_GetPINInfo",
      JSON.stringify({
        hDev: hDev,
        pinType: pinType,
      }),
    );
  };
  /**
   * 校验Pin
   * @param hDev 设备句柄
   * @param pin 旧口令
   * @param pinType 口令类型 0-admin 1-user
   */
  SKF.prototype.SKF_VerifyPIN = function (hDev, pin, pinType) {
    return this.postInvokeSrv(
      "SKF_VerifyPIN",
      JSON.stringify({
        hDev: hDev,
        pin: pin,
        pinType: pinType,
      }),
    );
  };
  /**
   * 解锁PIN
   * @param hDev     设备句柄
   * @param adminPin 管理口令
   * @param userPin  用户口令
   */
  SKF.prototype.SKF_UnblockPIN = function (hDev, adminPin, userPin) {
    return this.postInvokeSrv(
      "SKF_UnblockPIN",
      JSON.stringify({
        hDev: hDev,
        adminPin: adminPin,
        userPin: userPin,
      }),
    );
  };
  /**
   * 写入文件
   * @param hDev     设备句柄
   * @param fileName 文件名称
   * @param fileData 文件内容
   * @param dataIsBase64 内容格式 0-未编码 1-base64字符串
   * @param fileRights 文件权限
   */
  SKF.prototype.SKF_WriteFile = function (
    hDev,
    fileName,
    fileData,
    dataIsBase64,
    readRights,
    writeRights,
  ) {
    return this.postInvokeSrv(
      "SKF_WriteFile",
      JSON.stringify({
        hDev: hDev,
        fileName: fileName,
        fileData: fileData,
        dataIsBase64: dataIsBase64,
        readRights: readRights,
        writeRights: writeRights,
      }),
    );
  };
  /**
   * 读取文件
   * @param hDev     设备句柄
   * @param fileName 文件名称
   * @param dataIsBase64 内容格式 0-不编码 1-base64字符串
   */
  SKF.prototype.SKF_ReadFile = function (hDev, fileName, dataIsBase64) {
    return this.postInvokeSrv(
      "SKF_ReadFile",
      JSON.stringify({
        hDev: hDev,
        fileName: fileName,
        dataIsBase64: dataIsBase64,
      }),
    );
  };
  /**
   * 枚举文件
   * @param hDev 设备句柄
   */
  SKF.prototype.SKF_EnumFiles = function (hDev) {
    return this.postInvokeSrv(
      "SKF_EnumFiles",
      JSON.stringify({
        hDev: hDev,
      }),
    );
  };
  /**
   * 删除文件
   * @param hDev     设备句柄
   * @param fileName 文件名称
   */
  SKF.prototype.SKF_DeleteFile = function (hDev, fileName) {
    return this.postInvokeSrv(
      "SKF_DeleteFile",
      JSON.stringify({
        hDev: hDev,
        fileName: fileName,
      }),
    );
  };
  /**
   * 枚举容器
   * @param hDev 设备句柄
   */
  SKF.prototype.SKF_EnumContainer = function (hDev) {
    return this.postInvokeSrv(
      "SKF_EnumContainer",
      JSON.stringify({
        hDev: hDev,
      }),
    );
  };
  /**
   * 创建容器
   * @param hDev          设备句柄
   * @param containerName 容器名称
   */
  SKF.prototype.SKF_CreateContainer = function (hDev, containerName) {
    return this.postInvokeSrv(
      "SKF_CreateContainer",
      JSON.stringify({
        hDev: hDev,
        containerName: containerName,
      }),
    );
  };
  /**
   * 删除容器
   * @param hDev          设备句柄
   * @param containerName 容器名称
   */
  SKF.prototype.SKF_DeleteContainer = function (hDev, containerName) {
    return this.postInvokeSrv(
      "SKF_DeleteContainer",
      JSON.stringify({
        hDev: hDev,
        containerName: containerName,
      }),
    );
  };
  /**
   * 获取容器类型
   * @param hDev          设备句柄
   * @param containerName 容器名称
   */
  SKF.prototype.SKF_GetContainerType = function (hDev, containerName) {
    return this.postInvokeSrv(
      "SKF_GetContainerType",
      JSON.stringify({
        hDev: hDev,
        containerName: containerName,
      }),
    );
  };
  /**
   * 导入证书
   * @param hDev          设备句柄
   * @param cert          证书内容
   * @param certType      证书类型 1-sign,0-enc
   * @param containerName 容器名称
   */
  SKF.prototype.SKF_ImportCertificate = function (
    hDev,
    cert,
    certType,
    containerName,
  ) {
    return this.postInvokeSrv(
      "SKF_ImportCertificate",
      JSON.stringify({
        hDev: hDev,
        cert: cert,
        certType: certType,
        containerName: containerName,
      }),
    );
  };
  /**
   * 导出证书
   * @param hDev          设备句柄
   * @param containerName 容器名称
   * @param certType      证书类型 1-sign,0-enc
   */
  SKF.prototype.SKF_ExportCertificate = function (
    hDev,
    containerName,
    certType,
  ) {
    return this.postInvokeSrv(
      "SKF_ExportCertificate",
      JSON.stringify({
        hDev: hDev,
        containerName: containerName,
        certType: certType,
      }),
    );
  };
  /**
   * 生成随机数
   * @param hDev   设备句柄
   * @param rndLen 随机数长度
   */
  SKF.prototype.SKF_GenRandom = function (hDev, rndLen) {
    return this.postInvokeSrv(
      "SKF_GenRandom",
      JSON.stringify({
        hDev: hDev,
        rndLen: rndLen,
      }),
    );
  };
  /**
   * 生成RSA签名密钥对
   * @param hDev          设备句柄
   * @param containerName 容器名称
   * @param keyLength     密钥模长
   */
  SKF.prototype.SKF_GenRSAKeyPair = function (hDev, containerName, keyLength) {
    return this.postInvokeSrv(
      "SKF_GenRSAKeyPair",
      JSON.stringify({
        hDev: hDev,
        containerName: containerName,
        keyLength: keyLength,
      }),
    );
  };
  /**
   * 生成RSA签名密钥对(异步)
   * @param hDev 设备句柄
   * @param containerName 容器名称
   * @param keyLength 密钥模长
   * @param pubKeyFormat
   */
  SKF.prototype.SKF_GenRSAKeyPairAsync = function (
    hDev,
    containerName,
    keyLength,
    callback,
  ) {
    var xhr = new XMLHttpRequest();
    xhr.open("POST", this._srvUrl + "SKF_GenRSAKeyPair", true);
    xhr.setRequestHeader("Content-Type", "application/json; charset=utf-8");
    xhr.onreadystatechange = function () {
      if (xhr.readyState === 4) {
        if (xhr.status === 200) {
          callback(JSON.parse(xhr.responseText));
        } else {
          callback({
            status: xhr.status,
            msg: xhr.statusText,
          });
        }
      }
    };
    xhr.send(
      JSON.stringify({
        hDev: hDev,
        containerName: containerName,
        keyLength: keyLength,
      }),
    );
  };
  /**
   * 生成ECC签名密钥对
   * @param hDev          设备句柄
   * @param containerName 容器名称
   * @param keyLength     密钥模长
   */
  SKF.prototype.SKF_GenECCKeyPair = function (hDev, containerName) {
    return this.postInvokeSrv(
      "SKF_GenECCKeyPair",
      JSON.stringify({
        hDev: hDev,
        containerName: containerName,
      }),
    );
  };
  /**
   * 生成RSA加密密钥对密文
   * @param hDev 设备句柄
   * @param algType 对称算法类型
   * @param keyLength 对称密钥长度
   * @param signPubKey 签名公钥
   * @param encPubKey 加密公钥
   * @param encPriKey 加密私钥
   * @returns
   */
  SKF.prototype.SKF_GenRSACipherKeyPair = function (
    hDev,
    algType,
    keyLength,
    signPubKey,
    encPubKey,
    encPriKey,
  ) {
    return this.postInvokeSrv(
      "SKF_GenRSACipherKeyPair",
      JSON.stringify({
        hDev: hDev,
        algType: algType,
        keyLength: keyLength,
        signPubKey: signPubKey,
        encPubKey: encPubKey,
        encPriKey: encPriKey,
      }),
    );
  };
  /**
   * 生成ECC加密密钥对密文
   * @param hDev 设备句柄
   * @param algType 对称算法类型
   * @param keyLength 对称密钥长度
   * @param signPubKey 签名公钥
   * @param encPubKey 加密公钥
   * @param encPriKey 加密私钥
   * @returns
   */
  SKF.prototype.SKF_GenECCCipherKeyPair = function (
    hDev,
    algType,
    keyLength,
    signPubKey,
    encPubKey,
    encPriKey,
  ) {
    return this.postInvokeSrv(
      "SKF_GenECCCipherKeyPair",
      JSON.stringify({
        hDev: hDev,
        algType: algType,
        keyLength: keyLength,
        signPubKey: signPubKey,
        encPubKey: encPubKey,
        encPriKey: encPriKey,
      }),
    );
  };
  /**
   * 生成外部RSA密钥对
   * @param hDev 设备句柄
   * @param keyLength 密钥长度
   * @returns
   */
  SKF.prototype.SKF_GenExtRSAKey = function (hDev, keyLength) {
    return this.postInvokeSrv(
      "SKF_GenExtRSAKey",
      JSON.stringify({
        hDev: hDev,
        keyLength: keyLength,
      }),
    );
  };
  /**
   * 导入RSA加密密钥对
   * @param hDev          设备句柄
   * @param algType       对称密钥算法标识
   * @param wrappedKey    签名公钥保护的对称密钥
   * @param keyPairs      对称算法保护的RSA加密私钥
   * @param containerName 容器名称
   */
  SKF.prototype.SKF_ImportRSAKeyPair = function (
    hDev,
    algType,
    wrappedKey,
    keyPairs,
    containerName,
  ) {
    return this.postInvokeSrv(
      "SKF_ImportRSAKeyPair",
      JSON.stringify({
        hDev: hDev,
        algType: algType,
        wrappedKey: wrappedKey,
        keyPairs: keyPairs,
        containerName: containerName,
      }),
    );
  };
  /**
   * RSA签名
   * @param hDev          设备句柄
   * @param containerName 容器名称
   * @param data          base64编码待签名数据的杂凑
   */
  SKF.prototype.SKF_RSASignData = function (hDev, containerName, data) {
    return this.postInvokeSrv(
      "SKF_RSASignData",
      JSON.stringify({
        hDev: hDev,
        containerName: containerName,
        data: data,
      }),
    );
  };
  /**
     * RSA验证签名
     * @param hDev     设备句柄
     * @param pubKey   公钥
     * @param data     base64编码的待验证签名数据杂凑
     * @param signData 待验证签名值
     
     */
  SKF.prototype.SKF_RSAVerify = function (hDev, pubKey, data, signData) {
    return this.postInvokeSrv(
      "SKF_RSAVerify",
      JSON.stringify({
        hDev: hDev,
        pubKey: pubKey,
        data: data,
        signData: signData,
      }),
    );
  };
  /**
   * 导入ECC加密密钥对
   * @param hDev              设备句柄
   * @param containerName     容器名称
   * @param envelopedKey      base64编码的加密密钥对
   */
  SKF.prototype.SKF_ImportECCKeyPair = function (
    hDev,
    containerName,
    envelopedKey,
  ) {
    return this.postInvokeSrv(
      "SKF_ImportECCKeyPair",
      JSON.stringify({
        hDev: hDev,
        containerName: containerName,
        envelopedKey: envelopedKey,
      }),
    );
  };
  /**
   * 内部ECC签名
   * @param hDev              设备句柄
   * @param containerName     容器名称
   * @param data              base64编码的待签名数据杂凑值
   */
  SKF.prototype.SKF_ECCSignData = function (hDev, containerName, data) {
    return this.postInvokeSrv(
      "SKF_ECCSignData",
      JSON.stringify({
        hDev: hDev,
        containerName: containerName,
        data: data,
      }),
    );
  };
  /**
   * ECC外来公钥验签
   * @param hDev              设备句柄
   * @param pubKey            base64编码公钥
   * @param data              base64编码的待验签数据杂凑值
   * @param signData          base64编码的待验证签名值
   */
  SKF.prototype.SKF_ECCVerify = function (hDev, pubKey, data, signData) {
    return this.postInvokeSrv(
      "SKF_ECCVerify",
      JSON.stringify({
        hDev: hDev,
        pubKey: pubKey,
        data: data,
        signData: signData,
      }),
    );
  };
  /**
   * RSA公钥加密
   * @param hDev              设备句柄
   * @param pubKey            base64编码的公钥
   * @param data              待加密的明文数据
   * @param dataIsBase64      明文数据是否是base64 1-base64 0-ascii
   */
  SKF.prototype.SKF_RSAPubEncrypt = function (
    hDev,
    pubKey,
    data,
    dataIsBase64,
  ) {
    return this.postInvokeSrv(
      "SKF_RSAPubEncrypt",
      JSON.stringify({
        hDev: hDev,
        pubKey: pubKey,
        data: data,
        dataIsBase64: dataIsBase64,
      }),
    );
  };
  /**
   * RSA私钥解密
   * @param hDev          设备句柄
   * @param prvKey        base64编码的私钥
   * @param encData       待解密的数据
   * @param dataIsBase64  明文数据是否是base64 1-base64 0-ascii
   */
  SKF.prototype.SKF_RSAPrvDecrypt = function (
    hDev,
    prvKey,
    encData,
    dataIsBase64,
  ) {
    return this.postInvokeSrv(
      "SKF_RSAPrvDecrypt",
      JSON.stringify({
        hDev: hDev,
        prvKey: prvKey,
        encData: encData,
        dataIsBase64: dataIsBase64,
      }),
    );
  };
  /**
   * ECC公钥加密
   * @param hDev              设备句柄
   * @param pubKey            base64编码的公钥
   * @param data              待加密的明文数据
   * @param dataIsBase64      明文数据是否是base64 1-base64 0-ascii
   */
  SKF.prototype.SKF_ECCPubEncrypt = function (
    hDev,
    pubKey,
    data,
    dataIsBase64,
  ) {
    return this.postInvokeSrv(
      "SKF_ECCPubEncrypt",
      JSON.stringify({
        hDev: hDev,
        pubKey: pubKey,
        data: data,
        dataIsBase64: dataIsBase64,
      }),
    );
  };
  /**
   * ECC私钥解密
   * @param hDev          设备句柄
   * @param prvKey        base64编码的私钥
   * @param encData       待解密的数据
   * @param dataIsBase64  明文数据是否是base64 1-base64 0-ascii
   */
  SKF.prototype.SKF_ECCPrvDecrypt = function (
    hDev,
    prvKey,
    encData,
    dataIsBase64,
  ) {
    return this.postInvokeSrv(
      "SKF_ECCPrvDecrypt",
      JSON.stringify({
        hDev: hDev,
        prvKey: prvKey,
        encData: encData,
        dataIsBase64: dataIsBase64,
      }),
    );
  };
  /**
   * 导出公钥
   * @param hDev              设备句柄
   * @param containerName     容器名称
   * @param signFlag          是否签名公钥标识 1:签名公钥 0:加密公钥
   * @param pubKeyFormat      0-原结构体输出 1-按activx方式输出
   */
  SKF.prototype.SKF_ExportPublicKey = function (
    hDev,
    containerName,
    signFlag,
    pubKeyFormat,
  ) {
    return this.postInvokeSrv(
      "SKF_ExportPublicKey",
      JSON.stringify({
        hDev: hDev,
        containerName: containerName,
        signFlag: signFlag,
        pubKeyFormat: pubKeyFormat,
      }),
    );
  };
  /**
   * 杂凑初始化
   * @param hDev              设备句柄
   * @param algType           算法标识
   * @param pubKey          base64编码的签名者公钥
   * @param pucId          签名者ID
   * @param pucIdisBase64 签名者ID是否Base64 1-是 0-非
   */
  SKF.prototype.SKF_DigestInit = function (
    hDev,
    algType,
    pubKey,
    pucId,
    pucIdisBase64,
  ) {
    return this.postInvokeSrv(
      "SKF_DigestInit",
      JSON.stringify({
        hDev: hDev,
        algType: algType,
        pubKey: pubKey,
        pucId: pucId,
        pucIdisBase64: pucIdisBase64,
      }),
    );
  };
  /**
   * 单组数据杂凑
   * @param hHash             杂凑对象句柄
   * @param data              数据
   * @param dataFormat 数据格式 1-base64 0-未编码
   */
  SKF.prototype.SKF_Digest = function (hHash, data, dataFormat) {
    return this.postInvokeSrv(
      "SKF_Digest",
      JSON.stringify({
        hHash: hHash,
        data: data,
        dataFormat: dataFormat,
      }),
    );
  };
  /**
   * 多组数据杂凑
   * @param hHash      杂凑对象句柄
   * @param data       被杂凑数据
   */
  SKF.prototype.SKF_DigestUpdate = function (hHash, data) {
    return this.postInvokeSrv(
      "SKF_DigestUpdate",
      JSON.stringify({
        hHash: hHash,
        data: data,
      }),
    );
  };
  /**
   * 结束多组数据杂凑
   * @param hHash      杂凑对象句柄
   */
  SKF.prototype.SKF_DigestFinal = function (hHash) {
    return this.postInvokeSrv(
      "SKF_DigestFinal",
      JSON.stringify({
        hHash: hHash,
      }),
    );
  };
  /**
   * 销毁杂凑对象
   * @param hHash 杂凑对象
   * @returns
   */
  SKF.prototype.SKF_CloseDigestHandle = function (hHash) {
    return this.postInvokeSrv(
      "SKF_CloseDigestHandle",
      JSON.stringify({
        hHash: hHash,
      }),
    );
  };
  /**
   * 导入明文会话密钥
   * @param hDev              设备句柄
   * @param algType           算法标识
   * @param symmKey          base64编码的明文密钥
   */
  SKF.prototype.SKF_SetSymmKey = function (hDev, algType, symmKey) {
    return this.postInvokeSrv(
      "SKF_SetSymmKey",
      JSON.stringify({
        hDev: hDev,
        algType: algType,
        symmKey: symmKey,
      }),
    );
  };
  /**
   * 导入会话密钥
   * @param hDev              设备句柄
   * @param containerName     容器名称
   * @param encSymmKey        base64编码的密钥密文
   * @param algType           算法标识
   */
  SKF.prototype.SKF_ImportSymmKey = function (
    hDev,
    containerName,
    encSymmKey,
    algType,
  ) {
    return this.postInvokeSrv(
      "SKF_ImportSymmKey",
      JSON.stringify({
        hDev: hDev,
        containerName: containerName,
        encSymmKey: encSymmKey,
        algType: algType,
      }),
    );
  };
  /**
   * 关闭会话密钥句柄
   * @param hSymmKey          会话句柄
   */
  SKF.prototype.SKF_CloseSymmKey = function (hSymmKey) {
    return this.postInvokeSrv(
      "SKF_CloseSymmKey",
      JSON.stringify({
        hSymmKey: hSymmKey,
      }),
    );
  };
  /**
   * 加密初始化
   * @param hSymmKey          会话句柄
   * @param iv                base64编码的IV
   * @param paddingType       补位方式
   * @param feedBitLen        反馈值位长度
   */
  SKF.prototype.SKF_EncryptInit = function (
    hSymmKey,
    iv,
    paddingType,
    feedBitLen,
  ) {
    return this.postInvokeSrv(
      "SKF_EncryptInit",
      JSON.stringify({
        hSymmKey: hSymmKey,
        iv: iv,
        paddingType: paddingType,
        feedBitLen: feedBitLen,
      }),
    );
  };
  /**
   * 单组数据加密
   * @param hSymmKey          会话句柄
   * @param data              base64编码的明文数据
   * @param dataType          0-不作编码转换 1-base64 2-HEX编码
   * @param outDataType       密文输出格式,1-base64,2-HEX
   */
  SKF.prototype.SKF_Encrypt = function (hSymmKey, data, dataType, outDataType) {
    return this.postInvokeSrv(
      "SKF_Encrypt",
      JSON.stringify({
        hSymmKey: hSymmKey,
        data: data,
        dataType: dataType,
        outDataType: outDataType,
      }),
    );
  };
  /**
   * 解密初始化
   * @param hSymmKey          会话句柄
   * @param data              base64编码的密文数据
   */
  SKF.prototype.SKF_DecryptInit = function (
    hSymmKey,
    iv,
    paddingType,
    feedBitLen,
  ) {
    return this.postInvokeSrv(
      "SKF_DecryptInit",
      JSON.stringify({
        hSymmKey: hSymmKey,
        iv: iv,
        paddingType: paddingType,
        feedBitLen: feedBitLen,
      }),
    );
  };
  /**
   * 解密数据
   * @param hSymmKey          会话句柄
   * @param encData              base64编码的密文数据
   * @param encDataType       密文输出格式,1-base64,2-HEX
   * @param outDataType       0-不作编码转换 1-base64 2-HEX编码
   */
  SKF.prototype.SKF_Decrypt = function (
    hSymmKey,
    encData,
    encDataType,
    outDataType,
  ) {
    return this.postInvokeSrv(
      "SKF_Decrypt",
      JSON.stringify({
        hSymmKey: hSymmKey,
        encData: encData,
        encDataType: encDataType,
        outDataType: outDataType,
      }),
    );
  };
  /**
   * 枚举应用
   * @param hDev          设备句柄
   */
  SKF.prototype.SKF_EnumApplication = function (hDev) {
    return this.postInvokeSrv(
      "SKF_EnumApplication",
      JSON.stringify({
        hDev: hDev,
      }),
    );
  };
  /**
   * 打开应用
   * @param hDev          设备句柄
   * @param appName       应用名称
   */
  SKF.prototype.SKF_OpenApplication = function (hDev, appName) {
    return this.postInvokeSrv(
      "SKF_OpenApplication",
      JSON.stringify({
        hDev: hDev,
        appName: appName,
      }),
    );
  };
  /**
   * 外部RSA私钥签名
   * @param hDev 设备句柄
   * @param prvKey 签名私钥
   * @param data 待签名数据HASH
   */
  SKF.prototype.SKF_ExtRSAPriKeyOperation = function (hDev, prvKey, data) {
    return this.postInvokeSrv(
      "SKF_ExtRSAPriKeyOperation",
      JSON.stringify({
        hDev: hDev,
        prvKey: prvKey,
        data: data,
      }),
    );
  };
  /**
   * 外部ECC私钥签名
   * @param hDev 设备句柄
   * @param prvKey BASE64私钥
   * @param data BASE64待签名数据HASH
   * @returns
   */
  SKF.prototype.SKF_ExtECCSign = function (hDev, prvKey, data) {
    return this.postInvokeSrv(
      "SKF_ExtECCSign",
      JSON.stringify({
        hDev: hDev,
        prvKey: prvKey,
        data: data,
      }),
    );
  };
  /**
   * 内部ECC密钥解密
   * @param containerName 容器名称
   * @param keySpec 1-加密密钥,2-签名密钥
   * @param encData 密文
   */
  SKF.prototype.SKF_ECCDecrypt = function (
    hDev,
    containerName,
    keySpec,
    encData,
    dataIsBase64,
  ) {
    return this.postInvokeSrv(
      "SKF_ECCDecrypt",
      JSON.stringify({
        hDev: hDev,
        containerName: containerName,
        keySpec: keySpec,
        encData: encData,
        dataIsBase64: dataIsBase64,
      }),
    );
  };
  /**
   * 内部RSA密钥解密
   * @param containerName 容器名称
   * @param keySpec 1-加密密钥,2-签名密钥
   * @param encData 密文
   */
  SKF.prototype.SKF_RSADecrypt = function (
    hDev,
    containerName,
    keySpec,
    encData,
    dataIsBase64,
  ) {
    return this.postInvokeSrv(
      "SKF_RSADecrypt",
      JSON.stringify({
        hDev: hDev,
        containerName: containerName,
        keySpec: keySpec,
        encData: encData,
        dataIsBase64: dataIsBase64,
      }),
    );
  };
  /**
   * 加密指定的数据
   * @param key 密钥
   * @param data 数据
   */
  SKF.prototype.SW_GetCipher = function (key, data) {
    return this.postInvokeSrv(
      "SW_GetCipher",
      JSON.stringify({
        key: key,
        data: data,
      }),
    );
  };
  /**
   * 设备认证
   * @param hDev 设备句柄
   * @param authData 认证数据
   * @returns
   */
  SKF.prototype.SKF_DevAuth = function (hDev, authData) {
    return this.postInvokeSrv(
      "SKF_DevAuth",
      JSON.stringify({
        hDev: hDev,
        authData: authData,
      }),
    );
  };
  /**
   * 修改设备认证密钥
   * @param hDev 设备句柄
   * @param key Base64编码的密钥
   */
  SKF.prototype.SKF_ChangeDevAuthKey = function (hDev, key) {
    return this.postInvokeSrv(
      "SKF_ChangeDevAuthKey",
      JSON.stringify({
        hDev: hDev,
        key: key,
      }),
    );
  };
  /**
   * 创建应用
   * @param hDev 设备句柄
   * @param appName 应用名称
   * @param adminPin 管理员PIN
   * @param adminPinRetryCnt 管理员PIN最大重试次数
   * @param userPin 用户PIN
   * @param userPinRetryCnt 用户PIN最大重试次数
   * @param createFileRights 在该应用下创建文件和容器的权限
   */
  SKF.prototype.SKF_CreateApplication = function (
    hDev,
    appName,
    adminPin,
    adminPinRetryCnt,
    userPin,
    userPinRetryCnt,
    createFileRights,
  ) {
    return this.postInvokeSrv(
      "SKF_CreateApplication",
      JSON.stringify({
        hDev: hDev,
        appName: appName,
        adminPin: adminPin,
        adminPinRetryCnt: adminPinRetryCnt,
        userPin: userPin,
        userPinRetryCnt: userPinRetryCnt,
        createFileRights: createFileRights,
      }),
    );
  };
  /**
   * 删除应用
   * @param hDev 设备句柄
   * @param appName 应用名称
   */
  SKF.prototype.SKF_DeleteApplication = function (hDev, appName) {
    return this.postInvokeSrv(
      "SKF_DeleteApplication",
      JSON.stringify({
        hDev: hDev,
        appName: appName,
      }),
    );
  };
  /**
   * 手机盾生成CSR
   * @param hDev 设备句柄
   * @param containerName 容器名称
   * @returns
   */
  SKF.prototype.SKF_GenerateCSR = function (hDev, containerName, dn) {
    return this.postInvokeSrv(
      "SKF_GenerateCSR",
      JSON.stringify({
        hDev: hDev,
        containerName: containerName,
        dn: dn,
      }),
    );
  };
  return SKF;
})();
export default SKF;
