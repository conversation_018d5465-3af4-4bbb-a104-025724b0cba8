<template>
  <div class="uk-dig">
    <el-dialog
      :title="$t('placeholder.pin')"
      :visible.sync="dialogVisible"
      width="400px"
      :modal-append-to-body="true"
      :close-on-click-modal="false"
      :before-close="onCloseClick"
    >
      <div>
        <el-form
          @submit.native.prevent
          :model="ukForm"
          ref="ukFormRef"
          :rules="rules"
          :hide-required-asterisk="true"
        >
          <el-form-item prop="pin">
            <el-input
              v-model="ukForm.pin"
              type="password"
              :placeholder="$t('placeholder.place')"
              clearable
              id="pinId"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="cancel-btn oper-btn" @click="onCloseClick">{{
          $t("common.cancel")
        }}</el-button>
        <el-button type="primary" class="oper-btn" @click="onSubmitClick">{{
          $t("common.submit")
        }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: "uk-dig",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    let pinValidate = (rule, value, callback) => {
      let str = this.$ukey.login(value);
      if ("184549392" === str) {
        return callback(new Error(this.$t("validate.checkUKeyType")));
      }
      if (0 !== str) {
        return callback(new Error(this.$t("validate.uKeyPasswordError")));
      }
      return callback();
    };
    return {
      ukForm: {
        pin: "",
      },
      rules: {
        pin: [
          {
            required: true,
            message: this.$t("placeholder.pin"),
            trigger: "blur",
          },
          { validator: pinValidate, trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    onCloseClick() {
      this.$refs.ukFormRef.resetFields();
      this.$emit("eventClose");
    },
    onSubmitClick() {
      this.$refs.ukFormRef.validate((valid) => {
        if (valid) {
          this.$emit("eventSubmit", this.ukForm.pin);
          this.onCloseClick();
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.uk-dig {
  .el-dialog {
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    margin: 0px !important;
    .el-dialog__header {
      display: flex;
    }
  }
}
</style>
