<template>
  <div :class="inner ? 'common-header-inner' : 'common-header'">
    <el-page-header
      id="backBtnId"
      @back="goBack"
      :content="content"
      :title="title"
    >
    </el-page-header>
  </div>
</template>
<script>
export default {
  name: "common-header",
  props: {
    content: {
      type: String,
      default: "",
    },
    title: {
      type: String,
      default: "",
    },
    inner: {
      type: Boolean,
      default: false,
    },
    back: {
      type: Function,
      default: null,
    },
  },
  methods: {
    goBack() {
      return this.back ? this.back() : this.$router.go(-1);
    },
  },
};
</script>
<style lang="scss" scoped>
.common-header {
  height: 42px;
  line-height: 42px;
  background: #fff;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 6%);
  border-radius: 4px;
  .el-page-header {
    line-height: 42px;
    padding-left: 20px;
  }
}
.common-header-inner {
  ::v-deep .el-page-header__content {
    color: #72767b;
    font-size: 16px;
  }
}
</style>
