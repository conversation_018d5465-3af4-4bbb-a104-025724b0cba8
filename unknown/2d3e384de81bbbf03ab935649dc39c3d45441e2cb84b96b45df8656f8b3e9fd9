<template>
  <div class="base-tips" :class="{ [type]: true }">
    <i :class="icon"></i>
    <template v-if="$slots.title">
      <slot name="title"></slot>
    </template>
    <template v-else>
      {{ title }}
    </template>
    <slot name="default"></slot>
  </div>
</template>
<script>
export default {
  name: "alertTips",
  props: {
    type: {
      type: String,
      default: "primary", // primary,success,warning,danger,info
    },
    icon: {
      type: String,
      default: "el-icon-warning",
    },
    title: {
      type: String,
      default: "",
    },
  },
  data() {
    return {};
  },
};
</script>
<style lang="scss" scoped>
@import "@/assets/styles/variables.scss";
.base-tips {
  padding: 8px 16px;
  display: flex;
  align-items: flex-start;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  line-height: 22px;
  border: 1px solid;
  border-radius: 4px;
  i {
    display: inline-block;
    width: 16px;
    height: 16px;
    font-size: 16px;
    margin-right: 8px;
    margin-top: 4px;
  }
}
.primary {
  border-color: var(--color-primary);
  background-color: var(--color-primary19);
  i {
    color: var(--color-primary);
  }
}
.warning {
  border-color: $warning;
  background-color: $light-warning;
  i {
    color: $warning;
  }
}
</style>
