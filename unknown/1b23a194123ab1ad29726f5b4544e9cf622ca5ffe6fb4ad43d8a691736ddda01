<template>
  <div class="theme-system-container">
    <!-- <div
      class="left-box setting-drawer-block-checbox"
      style="display: flex; align-items: flex-start"
    >
      <div
        @click="onClickSaveDefaultTheme(true)"
        class="add-theme-box setting-drawer-block-checbox-item"
      >
        <div class="set-default-icon-box">
          <i class="set-default-icon el-icon-brush"></i>
        </div>

        <div class="item-themeName">设置默认主题</div>
      </div>
    </div> -->
    <div class="center-box setting-drawer-block-checbox">
      <sidebar-svg
        v-for="(item, i) in themeAll"
        :key="i"
        :themeData="item"
        @handleTheme="handleTheme"
        @deleteTheme="deleteTheme"
        v-bind="$attrs"
      ></sidebar-svg>
      <!-- 增加主题 接口/本地 -->

      <!-- <div
        @click="onClickAddTheme(true)"
        class="add-theme-box setting-drawer-block-checbox-item"
      >
        <i class="el-icon-upload"></i>
        <div class="item-themeName">{{ $t("themeConfig.addTheme") }}</div>
      </div> -->
    </div>

    <!-- <div
      @click="onClickAddTheme(false)"
      class="add-theme-box setting-drawer-block-checbox-item"
    >
      <i class="el-icon-upload"></i>
      <div class="item-themeName">{{ $t("themeConfig.addTheme") }}</div>
    </div> -->
    <choose-file-dig
      :dialogVisible="dialogVisible"
      :themeAll="themeAll"
      @eventClose="eventClose"
      @addTheme="addTheme"
      v-bind="$attrs"
    ></choose-file-dig>
  </div>
</template>

<script>
import SidebarSvg from "./sidebarSvg.vue";
import ChooseFileDig from "./chooseFileDig.vue";
import { formatColor_deep } from "@/utils/index";

export default {
  name: "ThemeSystem",
  components: {
    SidebarSvg,
    ChooseFileDig,
  },
  data() {
    return {
      addType: true,
    };
  },
  props: {
    themeAll: {
      type: Array,
      default: () => [],
    },
    defaultThemeObj: {
      type: Object,
      default: () => {},
    },
    dialogVisible: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    formatColor_deep,
    // 选择主题
    handleTheme(val) {
      this.$emit("handleTheme", val);
    },
    // 增加主题
    addTheme(file) {
      //   this.$emit("addTheme", file, this.addType);
      this.$emit("addTheme", file, this.addType);
    },
    onClickSaveDefaultTheme() {
      this.$emit("openDig", "configDig");
    },
    onClickAddTheme(type) {
      this.addType = type;
      this.$emit("openDig", "dialogVisible");
    },
    // 删除主题
    deleteTheme(data, type) {
      this.$emit("deleteTheme", data, type);
    },
    eventClose() {
      this.$emit("eventClose");
    },
  },
};
</script>

<style lang="scss" scoped>
.theme-system-container {
  display: flex;
  flex-wrap: nowrap;
}

.setting-drawer-block-checbox {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  margin-top: 10px;

  .add-theme-box {
    position: relative;
    margin-right: 30px;
    margin-bottom: 60px;
    border-radius: 2px;
    cursor: pointer;
    width: 100px;
    height: 45px;
    text-align: center;
    .item-themeName {
      margin-top: 9px;
    }

    i {
      display: inline-block;
      width: 48px;
      height: 42px;
      line-height: 40px;
      border-radius: 4px;
      text-align: center;
      font-size: 30px;
      color: var(--color-primary);
      box-shadow: 0px 1px 2px 1px rgba(0, 0, 0, 0.1);
    }
    .set-default-icon-box {
      display: inline-block;
      width: 48px;
      height: 42px;
      line-height: 40px;
      border-radius: 4px;
      text-align: center;
      font-size: 30px;
      color: var(--color-primary);
      box-shadow: 0px 1px 2px 1px rgba(0, 0, 0, 0.1);
    }
    .set-default-icon {
      background-image: linear-gradient(
        60deg,
        #ee82ee 25%,
        #6495ed,
        #76eec6 65%,
        #6495ed 80%,
        #ee82ee
      );
      -webkit-background-clip: text;
      color: transparent;
      animation: move 12s infinite linear;
    }
  }
}
@keyframes move {
  0% {
    background-position: 0 0;
  }
  //   25% {
  //     background-position: -10px 0;
  //   }
  50% {
    background-position: 10px -10px;
  }
  //   75% {
  //     background-position: 10px 0;
  //   }
  100% {
    background-position: 0 0;
  }
}
</style>
