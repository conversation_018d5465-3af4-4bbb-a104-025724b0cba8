<template>
  <div class="themePanel-container">
    <div class="themePanel-container-part">
      <div class="themePanel-scroll">
        <div class="drawer-container">
          <!-- LOGO配置 -->
          <div class="setting-drawer-content">
            <div class="setting-drawer-head">
              <h2 class="drawer-head">
                {{ $t("themeConfig.logoConfig") }}
              </h2>
              <!-- 导出/保存/重置 -->
              <div class="drawer-head-btns">
                <el-button
                  size="small"
                  class="search-btn"
                  type="primary"
                  @click="saveLogoSetting"
                  >{{ $t("common.save") }}</el-button
                >
                <el-button
                  size="small"
                  class="search-btn cancel-btn"
                  @click="resetLogoSetting"
                  >{{ $t("common.reset") }}</el-button
                >
              </div>
            </div>
          </div>
          <el-divider />
          <!-- 登录页 -->
          <div class="setting-drawer-content two-container">
            <div class="left-container">
              <div class="setting-drawer-title">
                <h3 class="drawer-title">{{ $t("themeConfig.loginPage") }}</h3>
              </div>
              <!-- 登录页LOGO -->
              <div class="drawer-item">
                <div class="drawer-item-title">
                  {{ $t("themeConfig.loginLogo") }}
                </div>
                <div class="drawer-item">
                  <ImageCustom
                    class="upload-demo w300"
                    accept="image/*"
                    name_file="loginLogo"
                    @setIcon="setIcon"
                  >
                    <el-button size="small" class="w300" type="primary">{{
                      $t("common.clickUpload")
                    }}</el-button>
                  </ImageCustom>
                </div>
              </div>
              <!-- 登录页封面 -->
              <div class="drawer-item">
                <div class="drawer-item-title">
                  {{ $t("themeConfig.loginCover") }}
                </div>
                <div class="drawer-item">
                  <ImageCustom
                    class="upload-demo w300"
                    accept="image/*"
                    name_file="loginCover"
                    @setIcon="setIcon"
                  >
                    <el-button size="small" class="w300" type="primary">{{
                      $t("common.clickUpload")
                    }}</el-button>
                  </ImageCustom>
                </div>
              </div>
            </div>
            <!-- 效果展示区  -->
            <div class="right-container login-demo-right-contaner">
              <div class="demo-tip">效果预览</div>
              <div class="login-demo-box">
                <div class="login-logo" v-if="loginLogo">
                  <img :src="loginLogo" />
                </div>
                <div class="login-cover" v-if="loginCover">
                  <div class="left-box">
                    <img :src="loginCover" />
                  </div>
                  <div class="right-box">效果预览</div>
                </div>
              </div>
            </div>
          </div>
          <!-- 侧边栏 -->
          <div class="setting-drawer-content two-container">
            <div class="left-container">
              <div class="setting-drawer-title">
                <h3 class="drawer-title">{{ $t("themeConfig.sidebar") }}</h3>
              </div>
              <!-- 左侧图标 -->
              <div class="drawer-item">
                <div class="drawer-item-title">
                  {{ $t("themeConfig.leftIcon") }}
                  <el-tooltip
                    class="mgl5"
                    effect="dark"
                    :content="$t('themeConfig.leftIconTip')"
                    placement="right"
                  >
                    <span
                      class="el-icon-question"
                      style="color: #606266"
                    ></span>
                  </el-tooltip>
                </div>
                <div class="drawer-item">
                  <ImageCustom
                    class="upload-demo w300"
                    accept="image/*"
                    name_file="logoIcon"
                    @setIcon="setIcon"
                  >
                    <el-button size="small" class="w300" type="primary">{{
                      $t("common.clickUpload")
                    }}</el-button>
                  </ImageCustom>
                </div>
              </div>
              <!-- 右侧标题 -->
              <div class="drawer-item">
                <div class="drawer-item-title">
                  {{ $t("themeConfig.rightTitle") }}
                  <el-tooltip
                    class="mgl5"
                    effect="dark"
                    :content="$t('themeConfig.rightTitleTip')"
                    placement="right"
                  >
                    <span
                      class="el-icon-question"
                      style="color: #606266"
                    ></span>
                  </el-tooltip>
                </div>
                <!-- 标题类型 -->
                <el-radio-group v-model="isTitleLogo" class="drawer-item">
                  <el-radio :label="false">{{
                    $t("themeConfig.titleImage")
                  }}</el-radio>
                  <el-radio :label="true">{{
                    $t("themeConfig.titleText")
                  }}</el-radio>
                </el-radio-group>
                <!-- 文字标题区 -->
                <div v-if="isTitleLogo">
                  <div class="drawer-item">
                    <el-input
                      size="small"
                      v-model="titleLogo"
                      clearable
                      class="drawer-item-input"
                      maxlength="16"
                    ></el-input>
                  </div>
                  <!-- 是否斜体 -->
                  <div class="drawer-item">
                    <span>{{ $t("themeConfig.fontStyle") }}</span>
                    <el-switch
                      v-model="titleLogoFontStyle"
                      class="drawer-switch"
                    />
                  </div>
                  <!-- 颜色 -->
                  <div class="drawer-item">
                    <span>{{ $t("themeConfig.color") }}</span>
                    <color-settings
                      :isAddClass="false"
                      style="float: right; margin: -3px 0px 0 0"
                      @setColor="setColor"
                      colorName="titleLogoColor"
                    ></color-settings>
                  </div>
                </div>
                <!-- 图片标题区 -->
                <div class="drawer-item" v-else>
                  <ImageCustom
                    class="upload-demo w300"
                    accept="image/*"
                    name_file="imageLogo"
                    @setIcon="setIcon"
                  >
                    <el-button size="small" class="w300" type="primary">{{
                      $t("common.clickUpload")
                    }}</el-button>
                  </ImageCustom>
                </div>
              </div>
            </div>
            <!-- 效果展示区  -->
            <div class="right-container">
              <div class="demo-tip">{{ $t("themeConfig.displayArea") }}</div>
              <div class="sidebar-logo-demo-box">
                <div
                  v-if="opened"
                  class="sidebar-logo-demo"
                  :style="{
                    backgroundColor:
                      sideTheme == 'theme-custom'
                        ? menuBackgroundCustom
                        : sideTheme === 'theme-dark'
                        ? variables.menuBackground
                        : settings.sideTheme === 'theme-theme'
                        ? settings.theme
                        : variables.menuLightBackground,
                  }"
                >
                  <img v-if="logoIcon" :src="logoIcon" class="sidebar-logo" />
                  <h1
                    v-if="isTitleLogo"
                    class="sidebar-title"
                    :style="{
                      color: titleLogoColor,
                      fontSize: '25px',
                      fontStyle: titleLogoFontStyle ? 'italic' : 'normal',
                    }"
                  >
                    {{ titleLogo }}
                  </h1>
                  <img
                    v-if="!isTitleLogo && imageLogo"
                    :src="imageLogo"
                    class="sidebar-logo-name"
                  />
                </div>
                <div
                  v-else
                  class="sidebar-logo-demo-small"
                  :style="{
                    backgroundColor:
                      sideTheme == 'theme-custom'
                        ? menuBackgroundCustom
                        : sideTheme === 'theme-dark'
                        ? variables.menuBackground
                        : settings.sideTheme === 'theme-theme'
                        ? settings.theme
                        : variables.menuLightBackground,
                  }"
                >
                  <img v-if="logoIcon" :src="logoIcon" class="sidebar-logo" />
                  <h1
                    v-if="!logoIcon && isTitleLogo"
                    class="sidebar-title"
                    :style="{
                      color: titleLogoColor,
                      fontSize: '25px',
                      fontStyle: titleLogoFontStyle ? 'italic' : 'normal',
                    }"
                  >
                    {{ titleLogo }}
                  </h1>
                  <img
                    v-if="!logoIcon && !isTitleLogo && imageLogo"
                    :src="imageLogo"
                    class="sidebar-logo-name"
                  />
                </div>
                <hamburger
                  :is-active="opened"
                  class="hamburger-container"
                  @toggleClick="toggleSideBar"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Hamburger from "@/components/Hamburger";
import ImageCustom from "../imageCustom.vue";
import ColorSettings from "../colorSettings.vue";
import variables from "@/assets/styles/variables.scss";
// import { uploadImgInfo } from "@/api/uploadImg";
import { mapState } from "vuex";
export default {
  name: "LogoSettings",
  components: { Hamburger, ColorSettings, ImageCustom },
  data() {
    return {
      logoIcon: this.$store.state.settings.logoIcon,
      // isTitleLogo: this.$store.state.settings.isTitleLogo,
      // titleLogo: this.$store.state.settings.titleLogo,
      titleLogoColor: this.$store.state.settings.titleLogoColor,
      // titleLogoFontStyle: this.$store.state.settings.titleLogoFontStyle,
      imageLogo: this.$store.state.settings.imageLogo,
      opened: true,
      loginLogo: this.$store.state.settings.loginLogo,
      loginCover: this.$store.state.settings.loginCover,
      files: {},
    };
  },
  computed: {
    ...mapState(["settings"]),
    isTitleLogo: {
      // 是否为文字标题（右）
      get() {
        return this.$store.state.settings.isTitleLogo;
      },
      set(val) {
        this.storeChange("isTitleLogo", val);
      },
    },
    titleLogo: {
      // 文字标题
      get() {
        return this.$store.state.settings.titleLogo;
      },
      set(val) {
        this.storeChange("titleLogo", val);
      },
    },
    titleLogoFontStyle: {
      // 文字标题-字号
      get() {
        return this.$store.state.settings.titleLogoFontStyle;
      },
      set(val) {
        this.storeChange("titleLogoFontStyle", val);
      },
    },
    variables() {
      return variables;
    },
    sideTheme() {
      return this.$store.state.settings.sideTheme;
    },
    menuBackgroundCustom() {
      return this.$store.state.settings.menuBackgroundCustom;
    },
  },
  methods: {
    toggleSideBar() {
      this.opened = !this.opened;
    },
    storeChange(attr, val) {
      this.$store.dispatch("settings/changeSetting", {
        key: attr,
        value: val,
      });
    },
    attrChange(attr, val) {
      this.$store.dispatch("settings/changeSetting", {
        key: attr,
        value: val,
      });
      this[attr] = val;
    },
    setColor(colorName, color) {
      this.attrChange(colorName, color);
    },
    setIcon(name_file, file) {
      if (file) {
        this.files[name_file] = file;
        let img = new FileReader();
        img.readAsDataURL(file);
        img.onload = ({ target }) => {
          let result = target.result; // 将img转化为二进制数据
          this.attrChange(name_file, result);
        };
      } else {
        this.attrChange(name_file, "");
      }
    },
    uploadImg(file, type) {
      const d = new FormData();
      d.append("file", file);
      d.append("imageCode", type);
      uploadImgInfo(d).then((res) => {
        // this.successMsg(res.msg);
        // 调用获取接口
        // this.$store.dispatch("getSysImg");
      });
    },
    async saveLogoSetting() {
      this.$modal.loading(this.$t("tips.saveThemeConfigLoading"));
      this.$cache.local.set(
        "logo-setting",
        `{
            "logoIcon":"${this.logoIcon}",
            "isTitleLogo":${this.isTitleLogo},
            "titleLogo":"${this.titleLogo}",
            "titleLogoColor":"${this.titleLogoColor}",
            "titleLogoFontStyle":${this.titleLogoFontStyle},
            "imageLogo":"${this.imageLogo}",
            "loginLogo": "${this.loginLogo}",
            "loginCover": "${this.loginCover}"
          }`,
      );
      try {
        if (files.logoIcon) {
          await this.uploadImg(val, "");
        }
        if (files.imageLogo) {
          await this.uploadImg(val, "");
        }
        if (files.loginLogo) {
          await this.uploadImg(val, "");
        }
        if (files.loginCover) {
          await this.uploadImg(val, "");
        }
      } catch {
        // console.log(e);
      }
      setTimeout(this.$modal.closeLoading(), 1000);
    },
    resetLogoSetting() {},

    eventClose() {
      this.toSysDigVisible = false;
    },
  },
  // beforeRouteLeave(to, from, next) {
  //   let flag = true;
  //   for (let key in this.currentThemeObj_copy) {
  //     if (this.currentThemeObj_copy[key] != this.currentThemeObj[key]) {
  //       flag = false;
  //     }
  //   }
  //   if (!flag) {
  //     this.confirmMessage(this.$t("tips.setThemeConfigTip"))
  //       .then(async () => {
  //         await this.saveSetting();
  //         console.log(this.toSysDigVisible);
  //         if (!this.toSysDigVisible) {
  //           next();
  //         }
  //       })
  //       .catch(async () => {
  //         this.$modal.loading(this.$t("tips.removeLoading"));
  //         setTimeout("window.location.reload()", 1000);
  //         next();
  //       });
  //   } else {
  //     next();
  //   }
  // },
};
</script>

<style lang="scss" scoped>
.themePanel-container {
  width: 100%;
  height: 100%;
  // overflow: auto;
  .themePanel-container-part {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0px 0px 10px 4px var(--color-primary19);
  }
  .themePanel-scroll {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .two-container {
    display: flex;
    .left-container {
      width: 450px;
    }
    .right-container {
      margin-top: 180px;
    }
    .login-demo-right-contaner {
      margin-top: 20px;
      img {
        width: 100%;
        height: 100%;
      }
      .login-logo {
        width: 122px;
        height: 40px;
      }
      .login-cover {
        width: 550px;
        height: 280px;
        display: flex;
        margin-top: 10px;
        margin-left: 170px;
        // border: 1px dotted #e6e6e6;
        border-radius: 4px;
        overflow: hidden;
        box-shadow: 0px 0px 67px 16px var(--color-primary29);

        .left-box {
          width: 185px;
        }
        .right-box {
          height: 100%;
          font-size: 30px;
          display: flex;
          justify-content: center;
          align-items: center;
          flex: 1;
        }
      }
    }
  }
  .sidebar-logo-demo-box {
    display: flex;
    .hamburger-container {
      display: inline-block;
      line-height: 56px;
      height: 56px;
      cursor: pointer;
      transition: background 0.3s;
      -webkit-tap-highlight-color: transparent;
    }
    .sidebar-logo-demo-small {
      width: 54px;
    }
    .sidebar-logo-demo {
      width: 224px;
    }
    .sidebar-logo-demo,
    .sidebar-logo-demo-small {
      box-shadow: 1px 2px 2px 3px #e6e6e6;
      height: 56px;
      text-align: center;
      line-height: 55px;
      background-color: #ccc;
      overflow: hidden;
      .sidebar-logo {
        width: 32px;
        height: 32px;
        vertical-align: middle;
        margin: 0 12px;
      }
      .sidebar-logo-name {
        display: inline-block;
        height: 32px;
        width: 100px;
        vertical-align: middle;
      }
      .sidebar-title {
        display: inline-block;
        margin: 0;
        font-weight: 900;
        line-height: 56px;
        font-size: 25px;
        font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
        vertical-align: middle;
      }
    }
  }
}

.drawer-container {
  padding: 20px;
  font-size: 14px;
  line-height: 1.5;
  .setting-drawer-content {
    .setting-drawer-head {
      position: relative;
      .search-btn {
        width: 120px;
        height: 32px;
        padding: 0;
        line-height: 32px;
        font-size: 14px;
      }
      .drawer-head {
        font-size: 18px;
      }
      .drawer-head-btns {
        position: absolute;
        top: -5px;
        right: 0px;
      }
    }
    .setting-drawer-title {
      .drawer-title {
        margin: 15px 0;
        color: rgba(0, 0, 0, 0.85);
        font-size: 16px;
      }
    }
  }
  .drawer-item {
    width: 300px;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    margin: 12px 0;
    padding: 12px 0;
    min-height: 38px;
    margin-left: 12px;
    .drawer-item-title {
      font-size: 16px;
      color: var(--color-primary);
      margin-bottom: 15px;
    }
    .drawer-item-checkbox {
      padding: 12px 0;
    }
    .drawer-item-theme-color {
      display: block;
      height: 60px;
    }
  }

  .drawer-switch {
    float: right;
  }
}
</style>
<style lang="scss">
.themePanel-container {
  .drawer-container {
    .el-radio-group {
      display: flex;
      .el-radio {
        flex: 1;
      }
    }
  }
}
</style>
