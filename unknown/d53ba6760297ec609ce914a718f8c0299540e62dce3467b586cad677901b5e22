@import "./variables.scss";
@import "./element-variables.scss";
/**
 * 通用css样式布局处理
 * Copyright (c) 2019 common
 */

/** 基础通用 **/
.pt5 {
  padding-top: 5px;
}

.pr5 {
  padding-right: 5px;
}

.pb5 {
  padding-bottom: 5px;
}

.mt5 {
  margin-top: 5px;
}

.mr5 {
  margin-right: 5px;
}

.mb5 {
  margin-bottom: 5px;
}

.mb8 {
  margin-bottom: 8px;
}

.ml5 {
  margin-left: 5px;
}

.mt10 {
  margin-top: 10px;
}

.mr10 {
  margin-right: 10px;
}

.mb10 {
  margin-bottom: 10px;
}
.mb15 {
  margin-bottom: 15px;
}
.ml10 {
  margin-left: 10px;
}

.mt20 {
  margin-top: 20px;
}

.mr20 {
  margin-right: 20px;
}

.mb20 {
  margin-bottom: 20px;
}

.ml20 {
  margin-left: 20px;
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: inherit;
  font-weight: 500;
  line-height: 1.1;
  color: inherit;
}

.el-dialog:not(.is-fullscreen) {
  margin-top: 6vh !important;
}

.el-dialog__wrapper.scrollbar .el-dialog .el-dialog__body {
  overflow: auto;
  overflow-x: hidden;
  max-height: 70vh;
  padding: 10px 20px 0;
}

// .el-table {
// 	.el-table__header-wrapper, .el-table__fixed-header-wrapper {
// 		th {
// 			word-break: break-word;
// 			background-color: #f8f8f9;
// 			color: #515a6e;
// 			height: 40px;
// 			font-size: 13px;
// 		}
// 	}
// 	.el-table__body-wrapper {
// 		.el-button [class*="el-icon-"] + span {
// 			margin-left: 1px;
// 		}
// 	}
// }

/** 表单布局 **/
.form-header {
  font-size: 15px;
  color: #6379bb;
  border-bottom: 1px solid #ddd;
  margin: 8px 10px 25px 10px;
  padding-bottom: 5px;
}

/** 表格布局 **/
.pagination-container {
  position: relative;
  height: 25px;
  margin-bottom: 10px;
  margin-top: 15px;
  padding: 10px 20px !important;
}

/* tree border */
.tree-border {
  margin-top: 5px;
  border: 1px solid #e5e6e7;
  background: #ffffff none;
  border-radius: 4px;
}

.pagination-container .el-pagination {
  right: 0;
  position: absolute;
}

@media (max-width: 768px) {
  .pagination-container .el-pagination > .el-pagination__jump {
    display: none !important;
  }

  .pagination-container .el-pagination > .el-pagination__sizes {
    display: none !important;
  }
}

.el-table .fixed-width .el-button--mini {
  padding-left: 0;
  padding-right: 0;
  width: inherit;
}

/** 表格更多操作下拉样式 */
.el-table .el-dropdown-link {
  cursor: pointer;
  color: var(--color-primary);
  margin-left: 5px;
}

.el-table .el-dropdown,
.el-icon-arrow-down {
  font-size: 12px;
}

.el-tree-node__content > .el-checkbox {
  margin-right: 8px;
}

.list-group-striped > .list-group-item {
  border-left: 0;
  border-right: 0;
  border-radius: 0;
  padding-left: 0;
  padding-right: 0;
}

.list-group {
  padding-left: 0px;
  list-style: none;
}

.list-group-item {
  border-bottom: 1px solid #e7eaec;
  border-top: 1px solid #e7eaec;
  margin-bottom: -1px;
  padding: 11px 0px;
  font-size: 13px;
}

.pull-right {
  float: right !important;
}

.pull-left {
  float: left;
}

.el-card__header {
  padding: 14px 15px 7px;
  min-height: 40px;
}

.el-card__body {
  padding: 15px 20px 20px 20px;
}

.card-box {
  padding-right: 15px;
  padding-left: 15px;
  margin-bottom: 10px;
}

/* text color */
.text-navy {
  color: #1ab394;
}

.text-primary {
  color: inherit;
}

.text-success {
  color: $green;
}

.text-info {
  color: $info;
}

.text-warning {
  color: $warning;
}

.text-danger {
  color: $red;
}

.text-muted {
  color: #888888;
}

/* image */
.img-circle {
  border-radius: 50%;
}

.img-lg {
  width: 120px;
  height: 120px;
}

.avatar-upload-preview {
  position: absolute;
  top: 50%;
  transform: translate(50%, -50%);
  width: 200px;
  height: 200px;
  border-radius: 50%;
  box-shadow: 0 0 4px #ccc;
  overflow: hidden;
}

/* 拖拽列样式 */
.sortable-ghost {
  opacity: 0.8;
  color: #fff !important;
  background: #42b983 !important;
}

.top-right-btn {
  position: relative;
  float: right;
}

.mgl30 {
  margin-left: 30px;
}

.mgl16 {
  margin-left: 16px !important;
}
.mgl10 {
  margin-left: 10px;
}

.mgl40 {
  margin-left: 40px;
}

.mgl120 {
  margin-left: 120px;
}

.mgl80 {
  margin-left: 80px;
}

.mgl5 {
  margin-left: 5px;
}

.mgt70 {
  margin-top: 70px;
}

.mgt42 {
  margin-top: 42px;
}

.mgt16 {
  margin-top: 16px;
}

.mgt10 {
  margin-top: 10px;
}

.mgb16 {
  margin-bottom: 16px;
}
.mgr16 {
  margin-right: 16px;
}

.mgb10 {
  margin-bottom: 10px;
}
.mgr10 {
  margin-right: 10px;
}
.mgr40 {
  margin-right: 40px;
}
.mgr24 {
  margin-right: 24px;
}

.pdr24 {
  padding-right: 24px;
}

.pdr16 {
  padding-right: 16px;
}

.pdr12 {
  padding-right: 12px;
}

.pd16 {
  padding: 16px;
}

.pd8 {
  padding: 8px;
}

.mgb16 {
  margin-bottom: 16px;
}

.w260 {
  width: 260px;
}

.w170 {
  width: 170px;
}

.w240 {
  width: 240px;
}

.w116 {
  width: 116px;
}

.w360 {
  width: 360px;
}

.w300 {
  width: 300px;
}

.w274 {
  width: 274px;
}

.w500 {
  width: 500px;
}

.w160 {
  width: 160px;
}

.w88 {
  width: 88px;
}

.w220 {
  width: 220px;
}

.w180 {
  width: 180px;
}

.w234 {
  width: 234px;
}

.ukey-btn {
  height: 32px;
  width: 120px;
  line-height: 32px;
  padding: 0px;
  // background-color: var(--color-primary);
  font-size: 14px;
  margin-left: 6px;
  // border-color: var(--color-primary);
}

.login-btn {
  width: 380px;
  height: 48px;
  font-size: 20px;
  font-weight: normal;
  line-height: 20px;
}

.full-height {
  height: 100%;
}

//  .app-main>.full-height {
//  	box-shadow: 0px 0px 10px 4px var(--color-primary19);
//  }

.full-width {
  width: 100%;
}

.el-button--primary {
  border-color: var(--color-primary);
  background: var(--color-primary);
  color: #fff;
  &:focus {
    border-color: var(--color-primary);
    background: var(--color-primary);
  }
  &:hover {
    border-color: var(--color-primary89);
    background: var(--color-primary89);
  }

  &:active {
    border-color: var(--color-primary);
    background: var(--color-primary);
  }
}
.el-button--primary.is-disabled,
.el-button--primary.is-disabled:hover,
.el-button--primary.is-disabled:focus,
.el-button--primary.is-disabled:active {
  background-color: var(--color-primary89);
  border-color: var(--color-primary89);
}

.actions-container {
  display: flex;
}

.dig-content {
  display: flex;
  justify-content: center;
}

.succ-color {
  color: #40b450;
}

.fail-color {
  color: #ea4040;
}

.pending-color {
  color: var(--color-primary);
}

.icon-box {
  font-size: 16px;
}

.option-class {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.el-input__inner::placeholder {
  color: #c0c4cc;
}

/* 谷歌 */
.el-input__inner::-webkit-input-placeholder {
  color: #c0c4cc;
}

/* 火狐 */
.el-input__inner:-moz-placeholder {
  color: #c0c4cc;
}

/*ie*/
.el-input__inner:-ms-input-placeholder {
  color: #c0c4cc;
}

.error-text-color {
  color: #f56c6c;
}

// 把element-ui copy.scss中的代码移到这里，防止样式覆盖
.el-tag {
  background: var(--color-primary09);
  border-color: var(--color-primary39);
}
.el-tag.el-tag--danger {
  background-color: #fef0f0;
  border-color: #fde2e2;
}
.el-tag.el-tag--warning {
  background-color: #fdf6ec;
  border-color: #faecd8;
}
.el-tag.el-tag--info {
  background-color: #f4f4f5;
  border-color: #e9e9eb;
}
.el-tag.el-tag--success {
  background-color: #f0f9eb;
  border-color: #e1f3d8;
}
.el-input.is-disabled .el-input__inner {
  background-color: #fafafa;
  border-color: #bbbbbb;
}

.el-table .el-table__header-wrapper th,
.el-table .el-table__fixed-header-wrapper th {
  background: rgba(0, 0, 0, 0.02);
  //   height: 40px;
  //   // background-color: #DAE1EB;
  //   background-color: var(--color-primary49);
  //   border-color: #ccc;
  //   font-size: 14px;
  //   font-weight: bold;
  //   line-height: 14px;
  color: #222222;
}

// .el-table .el-table__body-wrapper tr.el-table__row:nth-child(2n) td {
//   background-color: var(--color-primary09);
// }

// .el-table .el-table__body-wrapper tr.el-table__row:hover td {
//   background-color: var(--color-primary19);
// }

// .el-table .el-table__body-wrapper tr.current-row td {
//   background-color: var(--color-primary19);
// }

.el-table__empty-text {
  font-size: 16px;
}

// .el-table th.el-table__cell.is-leaf,
// .el-table td.el-table__cell {
//   padding: 0;
//   line-height: 40px;
//   height: 40px;
//   border-color: #ccc;
//   font-size: 14px;
// }

.el-pagination {
  font-size: 14px;
  padding: 0px;
}

.el-dialog__header {
  border-bottom: 1px solid #ccc;

  .el-dialog__title {
    color: #222222;
    font-size: 16px;
    line-height: 25px;
  }
}

.el-dialog__headerbtn {
  top: 23px;

  .el-dialog__close {
    color: #444444;
  }
}
//弹窗 保存、取消按钮
.el-dialog__footer,
.el-dialog__body {
  .dialog-footer {
    display: flex;
    // flex-direction: row-reverse;
    justify-content: center;
    .el-button {
      width: 120px;
      height: 32px;
      padding: 0;
      & + .el-button {
        margin-left: 10px;
        margin-right: 0px;
      }
    }
  }
}
.page-content-footer {
  display: flex;
  // flex-direction: row-reverse;
  justify-content: center;
  .el-button {
    width: 120px;
    height: 32px;
    padding: 0;
    & + .el-button {
      margin-left: 10px;
      margin-right: 0px;
    }
  }
}
// .search-table-content {
//   .search-table__body {

//分页列表隐藏边框
.el-table {
  border: none;
  &::before,
  &::after {
    display: none;
  }
  .el-table__fixed-right {
    &::before,
    &::after {
      display: none;
    }
  }
  .el-table__header {
    th.el-table__cell {
      background-color: #f7f9ff;
      border-right: none;
      &::after {
        content: "";
        position: absolute;
        right: 0;
        border-right: 1px solid #00000022;
        height: 70%;
        top: 15%;
      }
    }
  }
  .el-table__header-wrapper {
    th.el-table__cell {
      &:nth-last-of-type(2) {
        &::after {
          display: none;
        }
      }
    }
  }
  .el-table__fixed-header-wrapper {
    th.el-table__cell {
      &:nth-last-of-type(1) {
        &::after {
          display: none;
        }
      }
    }
  }
  .el-table__body-wrapper,
  .el-table__fixed-body-wrapper {
    td.el-table__cell {
      border-right: none;
    }
  }

  th.el-table__cell.is-leaf,
  td.el-table__cell {
    padding: 0;
    line-height: 40px;
    height: 40px;
    //border-color: #ccc;
    font-size: 14px;
    color: #444444;
  }
  .el-table__empty-text {
    font-size: 16px;
  }
}

//   }
// }
.search-table__header .el-form-item {
  margin-bottom: 16px;

  .el-form-item__label {
    line-height: 32px;

    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #212121;
  }

  .el-form-item__content {
    .el-input__inner {
      height: 32px;
      line-height: 32px;
    }

    .el-input__icon {
      line-height: 32px;
    }
    .el-cascader {
      line-height: 32px;
    }

    .el-range-editor.el-input__inner {
      padding: 3px 5px 0;

      .el-range__icon {
        line-height: 24px;
      }

      .el-range__close-icon {
        line-height: 24px;
        width: 20px;
      }
    }
  }
}

.el-form-item {
  margin-bottom: 24px;

  .el-form-item__label {
    line-height: 32px;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #212121;
  }

  .el-form-item__content {
    line-height: 32px;

    .el-input__inner {
      height: 32px;
      line-height: 32px;
    }

    .el-input__icon {
      line-height: 32px;
    }
    .el-cascader,
    .el-date-editor {
      line-height: 32px;
      height: 32px;
      width: 100%;
      .el-input__icon,
      .el-range-separator {
        line-height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .el-select {
      width: 100%;
    }
    .el-form-item__error {
      position: relative;
      word-break: break-word;
      margin-bottom: -16px;
    }
  }
  &.el-form-item--small {
    .el-form-item__error {
      margin-bottom: -14px;
    }
  }
}

.el-table .el-dropdown,
.el-icon-arrow-down {
  font-size: 14px !important;

  .el-dropdown-link {
    color: var(--color-primary);
  }

  i {
    color: #444444;
  }
}

.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.el-checkbox__inner:hover {
  border-color: var(--color-primary);
}

.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.el-radio__input.is-checked .el-radio__inner {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.el-radio__input.is-checked + .el-radio__label {
  color: var(--color-primary);
}

.el-tabs__item.is-active {
  color: var(--color-primary);
}

.el-tabs__active-bar {
  background-color: var(--color-primary);
}

.el-tabs__item:hover {
  color: var(--color-primary);
}

.el-descriptions-item__label:not(.is-bordered-label) {
  margin-right: 16px;
  color: #888888;
}

.el-descriptions-item__content {
  color: #444444;
}

.el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
  padding-bottom: 16px;
}

.el-dropdown-menu__item:not(.is-disabled):hover,
.el-dropdown-menu__item:focus {
  color: var(--color-primary);
  text-decoration: underline;
  background-color: var(--color-primary19);
}

.delete-text-btn.el-dropdown-menu__item:not(.is-disabled):hover,
.delete-text-btn.el-dropdown-menu__item:focus {
  color: #ea4040;
  text-decoration: underline;
}

.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
  color: var(--color-primary);
}

.el-tabs--border-card > .el-tabs__content {
  padding: 16px;
}

.el-select-dropdown__item.selected {
  color: var(--color-primary);
}
.el-select-dropdown__item.hover {
  background-color: var(--color-primary19);
}
.el-input-number {
  line-height: 32px;
}

.el-input-number__increase,
.el-input-number__decrease {
  height: 30px;
}

.el-textarea__inner {
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, SimSun, sans-serif;
}

.el-textarea.is-disabled .el-textarea__inner {
  background-color: #fafafa;
  border-color: #bbbbbb;
  color: #c0c4cc;
  cursor: not-allowed;
}

.el-drawer {
  .el-drawer__header {
    margin-bottom: 0px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ccc;
  }
  .el-drawer__footer {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 999;
    background: #fff;
    padding: 15px;
    border-color: rgba(0, 0, 0, 0.15);
    box-shadow: 0px 0px 24px 0px rgba(19, 58, 255, 0.12);
    text-align: right;
    .selection-footer {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .selection-total {
        color: rgba(0, 0, 0, 0.45);
        font-size: 14px;
        .selection-total-highlight {
          color: var(--color-primary);
        }
      }
    }
    .selection-btns {
    }
  }
}

// 弹窗中的表格
.table-dialog {
  .search-table-content .search-table__header {
    box-shadow: none;
    margin-bottom: 0;
  }
  .search-table-content .content-box {
    box-shadow: none;
  }
  .search-table-content .table-content {
    // padding: 0 16px 16px;
  }
}

.input-width-btn {
  display: flex;
  align-items: center;
  .el-button {
    padding: 5px;
    [class^="el-icon-"],
    [class*=" el-icon-"] {
      font-size: 16px;
    }
    & + .el-button {
      margin-left: 0px;
    }
  }
}

.vue-treeselect {
  .vue-treeselect__control {
    height: 30px;
    line-height: 32px;
  }

  .vue-treeselect__value-container {
    height: 28px;
    line-height: 32px;
    .vue-treeselect__single-value {
      color: #5e6d82;
      padding: 0 10px;
      line-height: 32px;
    }
    .vue-treeselect__placeholder {
      padding: 0 10px;
      line-height: 32px;
    }
    .vue-treeselect__multi-value {
      min-height: 30px;
      line-height: 32px;
      margin-bottom: 0;
      .vue-treeselect__multi-value-item-container {
        padding-top: 2px;
        padding-right: 6px;
        height: 24px;
        line-height: 24px;
        .vue-treeselect__multi-value-item {
          background-color: #f4f4f5;
          border-color: #e9e9eb;
          padding: 0;
          border-radius: 4px;
          .vue-treeselect__multi-value-label {
            color: #909399;
          }
          .vue-treeselect__icon {
            display: inline-block;
            padding-left: 4px;
            margin-right: 5px;
            color: #909399;
            background-color: #c0c4cc;
            height: 14px;
            width: 14px;
            line-height: 12px;
            vertical-align: middle;
            border-radius: 50%;
            &:hover {
              color: #ffffff;
              background-color: #909399;
            }
          }
        }
      }
    }
  }

  .vue-treeselect__menu-container {
    font-size: 14px;
    color: #5e6d82;

    .vue-treeselect__menu {
      .vue-treeselect__label {
        font-weight: 400;
      }
    }
  }
}
.vue-treeselect.vue-treeselect--searchable.vue-treeselect--multi.vue-treeselect--has-value
  .vue-treeselect__input-container {
  padding-top: 0px;
  height: 30px;
  line-height: 32px;
  .vue-treeselect__input {
    height: 30px;
    line-height: 32px;
    font-size: 14px;
    padding: 0;
  }
}

// 标题
.small-title {
  position: relative;
  padding: 5px 0px;
  font-size: 16px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 5px;
  // &.title-pre-bar {
  //   position: relative;
  //   &::before {
  //     content: "";
  //     position: absolute;
  //     top: 15%;
  //     display: inline-block;
  //     height: 70%;
  //     left: 0;
  //     width: 3px;
  //     background-color: #0677cd;
  //   }
  // }
  .more-btn {
    position: absolute;
    right: 0;
    top: 0;
  }
}

.sw-tabs {
  background: #ffffff;
  border-radius: 4px;
  .el-tabs__header {
    padding: 0px 20px;
  }
}

.sw-search-table-tabs {
  background: #ffffff;
  border-radius: 4px;
  .el-tabs__header {
    padding: 0px;
  }
}

.sw-steps {
  .el-step.is-vertical {
    flex-basis: unset !important;
    .el-step__head {
      width: 12px;
      .el-step__line {
        left: 5px;
        top: 6px;
        bottom: -6px;
        border-color: #d9d9d9;
      }
      .el-step__icon {
        position: absolute;
        top: 6px;
        width: 12px;
        height: 12px;
        &.el-step__icon {
          background: var(--color-primary);
          border-color: var(--color-primary);
        }
        .el-step__icon-inner {
          display: none;
        }
      }
      &.is-success {
        .el-step__icon {
          &.el-step__icon {
            background: var(--color-primary);
            border-color: var(--color-primary);
          }
        }
      }
      &.is-error {
        .el-step__icon {
          &.el-step__icon {
            border-color: #ff3c3c;
            background: #ff3c3c;
          }
        }
      }
    }
    .el-step__main {
      margin-left: 20px;
      .el-step__title {
        color: #262626;
        font-weight: 500;
        &.is-success {
          color: #262626;
        }
        &.is-error {
          color: #ff3c3c;
        }
      }
      .el-step__description {
        color: #8c8c8c;
        &.is-success {
          color: #8c8c8c;
        }
        &.is-error {
          color: #8c8c8c;
        }
      }
      .el-descriptions-item__label:not(.is-bordered-label) {
        color: #595959;
        font-weight: 500;
      }

      .el-descriptions-item__content {
        color: #8c8c8c;
      }
      .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
        padding-bottom: 8px;
      }
    }
  }
}
.sw-page-tips {
  margin-top: 5px;
  border-radius: 4px;
  padding: 2px 10px;
  border: 1px solid var(--color-primary);
  background-color: var(--color-primary19);
  color: #444;
  i {
    color: var(--color-primary);
  }
  &.warning {
    border-color: $--color-warning;
    background-color: transparentize($color: $--color-warning, $amount: 0.85);
    i {
      color: $--color-warning;
    }
  }
}
