<template>
  <div class="delete-dig">
    <el-dialog
      :title="$t('common.tips')"
      :visible="deleteDig"
      width="420px"
      top="10vh"
      :before-close="onDigClose"
      @open="onOpenDig"
    >
      <div class="delete-dig-content">
        <!-- 图标 -->
        <div class="dig-icon">
          <div class="el-icon-warning"></div>
        </div>
        <div class="dig-title">
          <div>{{ msg }}</div>
          <div>
            <el-checkbox v-model="checked">{{
              syncApp || $t("common.syncApp")
            }}</el-checkbox>
          </div>
        </div>
      </div>
      <div class="el-message-box__btns">
        <el-button size="small" @click="onDigClose">{{
          $t("common.cancel")
        }}</el-button>
        <el-button size="small" type="primary" @click="onDigSubmit">{{
          $t("common.submit")
        }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: "delete-dig",
  props: {
    deleteDig: {
      type: Boolean,
      default: false,
    },
    msg: {
      type: String,
      default: "",
    },
    syncApp: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      checked: true,
    };
  },
  methods: {
    onOpenDig() {
      this.checked = true;
    },
    onDigClose() {
      this.$emit("eventClose", false);
    },
    onDigSubmit() {
      this.$emit("eventSubmit", this.checked);
      this.onDigClose();
    },
  },
};
</script>
<style lang="scss">
.delete-dig {
  .el-dialog:not(.is-fullscreen) {
    margin-top: 40vh !important;
  }
  .el-dialog__header {
    border-bottom: none;
    padding: 15px;
    padding-bottom: 10px;
    .el-dialog__title {
      padding-left: 0;
      margin-bottom: 0;
      font-size: 18px;
      line-height: 1;
      color: #303133;
    }
  }
  .el-dialog__body {
    padding: 10px 15px;
  }
  .delete-dig-content {
    display: flex;
    .dig-icon {
      margin-right: 20px;
      color: #ffba00;
      font-size: 24px;
    }
    .el-checkbox__input.is-checked + .el-checkbox__label {
      color: #606266;
    }
  }
}
</style>
