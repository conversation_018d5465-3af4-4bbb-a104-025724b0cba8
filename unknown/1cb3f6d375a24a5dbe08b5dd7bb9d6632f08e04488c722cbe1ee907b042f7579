import { getImgInfo } from "@/api/theme";
import { getThemeById } from "@/api/theme";
import themeSystem from "@/utils/themeSystem.js"; // 默认系统主题[]
import { getPortalInfo } from "@/api/ai/portal";

const sys = {
  state: {
    version: "",
    language: "",
    loginImg: "",
    loginLogo: "",
    sysImg: "",
    sysLogo: "",
    favicon: "",
    themeObj: null,
    themeId: "",
    productName: null,
    isSysImgEnd: false, // 判断是否请求图片完成
  },

  mutations: {
    SET_SYS_VERSION: (state, version) => {
      state.version = version;
    },
    SET_SYS_IMG: (state, imgs) => {
      // 登录页
      state.loginImg = imgs.loginImg || "";
      state.loginLogo = imgs.loginLogo || "";
      //   侧边栏
      state.sysImg = imgs.sysImg || "";
      state.sysLogo = imgs.sysLogo || "";
      //   state.favicon = imgs.loginImg
      //     ? "data:image/png;base64," + imgs.loginImg
      //     : "";
    },
    SET_THEME_OBJ: (state, objStr) => {
      state.themeObj = objStr;
    },
    SET_THEME_ID: (state, id) => {
      state.themeId = id;
    },
    SET_FAVICON: (state, imgs) => {
      state.favicon = imgs.loginImg || "";
    },
    SET_PRODUCT_NAME: (state, productName) => {
      state.productName = productName;
    },
    SET_IS_SYS_IMG_END: (state, flag) => {
      state.isSysImgEnd = flag;
    },
  },

  actions: {
    getPortalInfo({ commit, state }, id) {
      commit("SET_IS_SYS_IMG_END", false);
      getPortalInfo().then((res) => {
        commit("SET_SYS_IMG", {
          // loginImg: res.data.portalLogo,
          sysImg: res.data.portalLogo,
          loginLogo: res.data.portalLogo,
          sysLogo: res.data.portalLogo,
        });
        var link = document.querySelector('link[rel*="icon"]');
        link.href = res.data.portalLogo || "";
        commit("SET_PRODUCT_NAME", res.data.portalTitle);
        commit("SET_IS_SYS_IMG_END", true);
      });
    },
    //   获取系统图片(四张图)
    getSysImg({ commit, state }, id) {
      commit("SET_IS_SYS_IMG_END", false);
      Promise.all([
        getImgInfo("loginPic", id),
        getImgInfo("systemLogo", id),
        getImgInfo("loginLogo", id),
        getImgInfo("sysLogo", id),
      ]).then((res) => {
        commit("SET_SYS_IMG", {
          loginImg: res[0].data,
          sysImg: res[1].data,
          loginLogo: res[2].data,
          sysLogo: res[3].data,
        });
        commit("SET_IS_SYS_IMG_END", true);
      });
    },
    getThemeObj({ commit, state }, id) {
      commit("SET_THEME_ID", id);
      return new Promise((resolve, reject) => {
        let themeObj = null;
        if (id.length === 3) {
          // 内部主题
          themeObj = themeSystem.find((item, i) => item.themeId == id);
          commit("SET_THEME_OBJ", JSON.stringify(themeObj) || null);
          resolve(JSON.stringify(themeObj));
        } else {
          // 外部主题
          getThemeById(id)
            .then((res) => {
              themeObj = res.data;
              commit("SET_THEME_OBJ", JSON.stringify(themeObj) || null);
              resolve(JSON.stringify(themeObj));
            })
            .catch((error) => {
              reject(error);
            });
        }
      });
    },
  },
};

export default sys;
