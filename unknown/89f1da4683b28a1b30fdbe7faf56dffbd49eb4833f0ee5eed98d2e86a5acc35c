import { WRSubject, intervalSubject } from "@/utils/rxjs";

export default {
  data() {
    return {
      observe: null,
    };
  },
  activated() {
    this.resetHeight();
  },
  mounted() {
    this.resetHeight();
    this.addResizeWatch();
  },
  beforeD<PERSON>roy() {
    this.removeResizeWatch();
  },
  methods: {
    // resetHeight() {
    //   // eslint-disable-next-line
    // },
    addResizeWatch() {
      this.observe = WRSubject.subscribe(() => {
        this.resetHeight();
      });
    },
    removeResizeWatch() {
      this.observe.unsubscribe();
    },
  },
};
