<template>
  <component :is="layout" />
</template>
<script>
import { getSession } from "@/utils/session";
import { getToken } from "@/utils/auth";
import Layout1 from "./layout1.vue";
import Layout2 from "./layout2.vue";
import Layout3 from "./layout3.vue";
import Layout4 from "./layout4.vue";
import LayoutInner from "./layoutInner.vue";
import LayoutNewStyle from "./layoutNewStyle.vue";
import LayoutNewStyleInner from "./layoutNewStyleInner.vue";
import difyLayout from "./difyLayout.vue";

export default {
  name: "Layout",
  components: {
    Layout1,
    Layout2,
    Layout3,
    Layout4,
    LayoutInner,
    LayoutNewStyle,
    LayoutNewStyleInner,
    difyLayout,
  },
  data() {
    return {
      layout: swGlobal.layoutStyle,
    };
  },
  methods: {
    // 全局postMessage事件
    messageEvt(event) {
      // token 和 withoutDownload 目前用于下载请求 分别表示 接收下载状态的凭证 和 是否不需要直接下载
      const {
        type,
        path,
        label,
        query,
        token,
        withoutDownload = false,
        alert,
        initSuccData,
        dialogValue,
        initValue,
        loadingData,
        reLoginType,
      } = event.data;

      const { oldPath, newPath } = event.data;

      if (type === "openNewView") {
        this.$router.push({
          path,
          meta: { title: label },
          query: { ...query, title: label },
        });
      } else if (type === "openNewIFrame") {
        this.$router.push({
          path: `/system/page/iframe/url=${encodeURIComponent(path)}`,
          meta: { title: "用户管理" },
        });
      } else if (type === "openNewWindow") {
        const otherWindow = window.open();
        otherWindow.opener = null;
        otherWindow.location = path;
      } else if (type === "closeTag") {
        window.$eventBus.$emit("closeTag", {
          path: `/common/page__iframeUrl=${encodeURIComponent(
            path,
          )}&title=${label}`,
        });
      } else if (type === "closeCurrentTag") {
        window.$eventBus.$emit("closeCurrentTag");
      } else if (type === "replaceTagView") {
        window.$eventBus.$emit("replaceCurrentTag", {
          oldView: { path: oldPath },
          newView: { path: newPath, fullPath: newPath, title: label, query },
        });
      } else if (type === "toggleScreenFull") {
        if (document.body.classList.contains("fullscreen-main")) {
          document.body.classList.remove("fullscreen-main");
        } else {
          document.body.classList.add("fullscreen-main");
        }
      } else if (type === "sessionTimeout") {
        window.$eventBus.$emit("sessionTimeout");
      } else if (type === "download") {
        window.$eventBus.$emit("download", {
          path,
          withoutDownload,
        });
      } else if (type === "downloadResponse") {
        // 本处做测试用，需要各个业务线集成响应返回数据
        console.log("响应下载结果", event.data);
      } else if (type === "loginUser") {
        window.$eventBus.$emit("loginUser", {
          token,
        });
      } else if (type === "alert") {
        this.$message({
          message: alert.message || "",
          duration: alert.duration || 2000,
          type: alert.type || "info",
        });
      } else if (type === "initSuccData") {
        window.$eventBus.$emit("initSuccData", {
          initSuccData,
        });
      } else if (type === "dialog") {
        window.$eventBus.$emit("dialog", {
          dialogValue,
        });
      } else if (type === "LicenceSucc") {
        window.$eventBus.$emit("LicenceSucc");
      } else if (type === "reLogin") {
        let isUk = getSession("isUkLogin");
        let msg = this.$t("common.reLoginTip");
        if (isUk && reLoginType) {
          if (reLoginType == 1) {
            // reLoginType: 1：未下载uk控件 2： 未插入uk  3：更换uk
            msg = this.$t("common.ukControl");
          } else if (reLoginType == 2) {
            msg = this.$t("common.unInsetUk");
          } else if (reLoginType == 3) {
            msg = this.$t("common.updateUk");
          }
        }
        this.confirmMessage(
          msg,
          "warning",
          false,
          false,
          false,
          this.$t("common.reLogin"),
          this.$t("common.sysTip"),
          false,
        )
          .then(() => {
            let url = this.getLogoutURL();
            if (getToken()) {
              this.$store.dispatch("LogOut").then((res) => {
                if (url) {
                  this.$store.dispatch("app/setGlobalLoading", true);
                  location.href = url;
                } else {
                  location.href = `${this.getBasePrefix()}login`;
                }
              });
            } else {
              this.$store.dispatch("FedLogOut").then((res) => {
                if (url) {
                  this.$store.dispatch("app/setGlobalLoading", true);
                  location.href = url;
                } else {
                  location.href = `${this.getBasePrefix()}login`;
                }
              });
            }
          })
          .catch(() => {});
      } else if (type === "fullLoad") {
        // 全局loading
        let loading = this.$loading({
          lock: true,
          background: loadingData?.background || "hsla(0,0%,100%,.9)",
          text: loadingData?.text || "Loading",
        });
        if (!loadingData.show) {
          loading.close();
        }
        window.$eventBus.$emit("fullIframeFixed", {
          fullFixed: loadingData.fullFixed,
        });
      } else if (type == "downloadUkControl") {
        // 下载uk控件
        this.confirmMessage(this.$t("initLogin.ukTip"))
          .then(() => {
            window.location.href = `${this.getBasePrefix(
              true,
            )}safe-usbkey_3.1.4.exe`;
          })
          .catch(() => {});
      }
    },
  },
  mounted() {
    window.addEventListener("message", this.messageEvt);
    // 调用消息通知接口，取出未读的列表
    // this.getUnReadMessage();
  },

  beforeDestroy() {
    window.removeEventListener("message", this.messageEvt);
  },
};
</script>
