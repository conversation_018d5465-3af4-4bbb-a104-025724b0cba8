import Operatebtns from "@/components/operatebtns/index.vue";
import { trimObjectValue } from "@/utils/util";
import { checkPermi } from "@/utils/permission";
export default {
  components: {
    Operatebtns,
  },
  data() {
    return {
      tablePage: {
        total: 0,
        pageSize: 20,
        pageNum: 1,
      },
      loading: false,
      tableData: [],
      searchTableRef: "searchTableRef",
      selectList: [],
      initQuery: true,
      operateBtns: [],
      operbtnsLimit: 2,
      showEditDialog: false,
    };
  },
  beforeMount() {
    this.setColumnsMinWidth();
    this.filterHasRoleOperBtns(this.operbtnsLimit);
  },
  mounted() {
    if (this.initQuery) {
      this.query();
    }
  },
  methods: {
    setColumnsMinWidth() {
      this.columns.forEach((item, idx) => {
        if (!(item.render == "columnOperate" || item.render == "operation")) {
          let width = Number(
            (item.width || item.minWidth || "").replace("px", ""),
          );
          let textWidth =
            this.getStringWidth(
              item.label,
              "bold 14px 'Montserrat', 'Helvetica Neue'",
            ) + 30;
          if (!width || width < textWidth) {
            delete item.width;
            item.minWidth = textWidth + "px";
          }
        }
      });
    },
    filterHasRoleOperBtns(limit) {
      //无操作列 无操作按钮
      if (!this.operateBtns || !this.columns) {
        return;
      }
      //过滤仅有权限按钮
      this.operateBtns =
        this.operateBtns?.filter((i) => {
          //有权限标识，则判断权限
          if (i.permi) {
            return checkPermi(i.permi);
          } else {
            return true;
          }
        }) || [];
      let operlen = this.operateBtns.length;
      if (operlen == 0) {
        // 无权限隐藏操作列
        this.columns.forEach((item, idx) => {
          if (item.render == "columnOperate" || item.render == "operation") {
            this.columns.splice(idx, 1);
          }
        });
        return;
      }
      let text = "";
      limit = limit || 999;
      this.operateBtns.forEach((item, idx) => {
        if (idx < limit) {
          text += item.name;
        }
        if (operlen == limit + 1 && idx == limit) {
          text += item.name;
        }
      });
      let width = this.getStringWidth(
        text,
        "14px 'Montserrat', 'Helvetica Neue', 'Microsoft YaHei'",
      );
      width += ((operlen > limit ? limit : operlen) - 1) * 10; //按钮间距
      width += 40; //td边距
      if (this.operateBtns.length > limit + 1) {
        width += 60;
      }
      if (width < 120) {
        width = 120;
      }
      // 更新操作列宽度
      this.columns.forEach((item, idx) => {
        if (item.render == "columnOperate" || item.render == "operation") {
          item.width = width + "px";
        }
      });
    },

    getStringWidth(str, font) {
      const div = document.createElement("div");
      div.innerText = str;
      div.style.font = font;
      div.style.display = "inline-block";
      div.style.visibility = "hidden";
      document.body.appendChild(div);
      const width = div.offsetWidth;
      document.body.removeChild(div);
      return width;
    },
    queryParams() {
      let params = {
        ...this.tablePage,
      };
      if (this.searchForm) {
        params = Object.assign(params, this.searchForm);
      }
      if (this.getExtraParams) {
        params = Object.assign(params, this.getExtraParams());
      }
      return trimObjectValue(params);
    },
    async query() {
      try {
        this.loading = true;
        const res = await this.doQuery();
        this.tableData = res.data.list ? res.data.list : res.data;
        this.tablePage.total = res.data.total;

        if (this.tableLoadData) {
          this.tableLoadData();
        }
        if (this.tablePage.total !== 0 && this.tableData.length === 0) {
          if (this.tablePage.pageNum > 1) {
            this.tablePage.pageNum--;
            this.query();
          }
          return;
        }
      } catch {
      } finally {
        this.loading = false;
        // 表格回到顶部
        if (this.$refs[this.searchTableRef]) {
          this.$refs[
            this.searchTableRef
          ].getTableRef().bodyWrapper.scrollTop = 0;
          this.$refs[this.searchTableRef].getTableRef().doLayout();
        }
      }
    },
    doQuery() {
      return this.request(this.queryParams());
    },
    resetParamsQuery(flag = false) {
      if (flag) {
        this.tablePage.pageNum = 1;
      }
      this.query();
    },
    // 重置
    reset() {
      this.$nextTick(() => {
        this.$refs[this.searchTableRef].resetField();
        this.resetParamsQuery(true);
        this.resetTableHeight();
      });
    },
    // 分页
    pagination(params) {
      Object.keys(params).forEach((item) => {
        this.tablePage[item] = params[item];
      });
      this.query();
    },
    // 按钮提示性操作
    confirmInfoOperFun(msg, request, params, succMsg, callback) {
      this.confirmMessage(msg).then(() => {
        this.loading = true;
        request(params)
          .then((res) => {
            this.successMsg(succMsg);
            this.query();
            if (callback) {
              callback();
            }
          })
          .finally(() => {
            this.loading = false;
          });
      });
    },
    selectionChange(selection) {
      this.selectList = selection;
    },
    closeEditDialog() {
      this.showEditDialog = false;
      this.$nextTick(() => {
        this.resetEditInfo();
      });
    },
    resetTableHeight() {
      this.$nextTick(() => {
        this.$refs[this.searchTableRef].resetHeight();
      });
    },
  },
};
