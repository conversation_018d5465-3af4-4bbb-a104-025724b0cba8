import { formatColor_deep } from "@/utils/index";
import { getThemeById } from "@/api/theme";
export default {
  computed: {
    webTitle() {
      return this.$store.state.settings.webTitle;
    },
    menuColorCustom() {
      return this.$store.state.settings.menuColorCustom;
    },
    menuColorActiveCustom() {
      return this.$store.state.settings.menuColorActiveCustom;
    },
    subMenuBackgroundActiveCustom() {
      return this.$store.state.settings.subMenuBackgroundActiveCustom;
    },
    subMenuBackgroundCustom() {
      return this.$store.state.settings.subMenuBackgroundCustom;
    },
    subMenuHoverCustom() {
      return this.$store.state.settings.subMenuHoverCustom;
    },
    topBackgroundCustom() {
      return this.$store.state.settings.topBackgroundCustom;
    },
    topSvgCustom() {
      return this.$store.state.settings.topSvgCustom;
    },
    theme() {
      return this.$store.state.settings.theme;
    },
    appStyleObj() {
      return {
        "--custom-menu-color": this.menuColorCustom,
        "--custom-menu-color-active": this.menuColorActiveCustom,
        "--custom-sub-menu-background-active":
          this.subMenuBackgroundActiveCustom,
        "--custom-sub-menu-background": this.subMenuBackgroundCustom,
        "--custom-sub-menu-hover": this.subMenuHoverCustom,
        "--custom-top-bg": this.topBackgroundCustom,
        "--custom-top-svg": this.topSvgCustom,
      };
    },
  },
  methods: {
    // 设置主题颜色
    setTheme(val) {
      // 设置所有iframe的主题（暂时存储）
      // 注意：第一次打开iframe时不执行，应该放在onload事件中，但要让onload生效，src必须在onload后面定义
      const existIframe = document.getElementsByTagName("iframe");
      for (let i = 0; i < existIframe.length; i += 1) {
        existIframe[i].contentWindow.postMessage(
          {
            type: "theme",
            themeVal: val,
          },
          existIframe[i].src,
        );
      }
      // 设置根元素:root主题样式
      document.documentElement.style.setProperty("--color-primary", val);
      document.documentElement.style.setProperty(
        "--color-primary19",
        val + "19",
      );
      document.documentElement.style.setProperty(
        "--color-primary09",
        val + "09",
      );
      document.documentElement.style.setProperty(
        "--color-primary29",
        val + "29",
      );
      document.documentElement.style.setProperty(
        "--color-primary-deep80",
        this.formatColor_deep(val, 80),
      );
      document.documentElement.style.setProperty(
        "--color-primary-deep50",
        this.formatColor_deep(val, 50),
      );
      document.documentElement.style.setProperty(
        "--color-primary49",
        val + "49",
      );
      document.documentElement.style.setProperty(
        "--color-primary89",
        val + "89",
      );
      this.storeChange("theme", val);
    },
    formatColor_deep,
    storeChange(attr, val) {
      this.$store.dispatch("settings/changeSetting", {
        key: attr,
        value: val,
      });
    },
    // 验证本地主题是否为系统主题
    async changeLocalTheme(layoutData) {
      let id = layoutData.themeId;
      let flag = true;
      if (id.length != 3) {
        let res = await getThemeById(id);
        if (!res.data || !res.data.themeNameZh) {
          flag = false;
        }
      } else {
        let obj = this.themeSystem.find((item) => item.themeId == id);
        if (!obj) {
          flag = false;
        }
      }
      if (!flag) {
        // 如果不是系统主题
        layoutData.themeNameZh = "自定义主题";
        layoutData.themeNameEn = "Custom Topic";
        layoutData.themeId = "000";
        // 本地存储
        this.$cache.local.set(
          "layout-setting",
          `{
               "webTitle":"${layoutData.webTitle}",
               "tagsView":${layoutData.tagsView},
               "sidebarLogo":${layoutData.sidebarLogo},
               "dynamicTitle":${layoutData.dynamicTitle},
               "sideTheme":"${layoutData.sideTheme}",
               "themeNameZh":"${layoutData.themeNameZh}",
               "themeNameEn":"${layoutData.themeNameEn}",
               "theme":"${layoutData.theme}",
               "themeId":"${layoutData.themeId}",
               "menuColorCustom":"${layoutData.menuColorCustom}",
               "menuBackgroundCustom":"${layoutData.menuBackgroundCustom}",
               "menuColorActiveCustom":"${layoutData.menuColorActiveCustom}",
               "subMenuBackgroundActiveCustom":"${layoutData.subMenuBackgroundActiveCustom}",
               "subMenuBackgroundCustom":"${layoutData.subMenuBackgroundCustom}",
               "subMenuHoverCustom":"${layoutData.subMenuHoverCustom}",
               "topBackgroundCustom":"${layoutData.topBackgroundCustom}",
               "topSvgCustom":"${layoutData.topSvgCustom}"
             }`,
        );
      }
    },
    // 系统主题-格式化样式属性
    initTheme(obj) {
      console.log(obj.webTitle);
      this.storeChange("webTitle", obj.webTitle);
      this.storeChange("tagsView", obj.tagsView);
      this.storeChange("sideTheme", obj.sideTheme);
      this.storeChange("theme", obj.theme);
      localStorage.setItem("SecTheme", obj.theme);
      this.storeChange("themeNameZh", obj.themeNameZh);
      this.storeChange("themeNameEn", obj.themeNameEn);
      this.storeChange("sidebarLogo", obj.sidebarLogo);
      // 如果不是theme-custom,需修改sidebar
      if (obj.sideTheme != "theme-custom") {
        // 如果是theme-theme,则自动勾选跟随主题颜色
        if (obj.sideTheme == "theme-theme") {
          // 手动修改sidebar属性
          this.storeChange("menuColorCustom", "#fff");
          this.storeChange("menuBackgroundCustom", obj.theme);
          this.storeChange("menuColorActiveCustom", "#fff");
          this.storeChange(
            "subMenuBackgroundCustom",
            this.formatColor_deep(obj.theme, 50),
          );
          this.storeChange(
            "subMenuBackgroundActiveCustom",
            this.formatColor_deep(obj.theme, 80),
          );
          this.storeChange(
            "subMenuHoverCustom",
            this.formatColor_deep(obj.theme, 80),
          );
        } else if (obj.sideTheme == "theme-light") {
          this.storeChange("menuColorCustom", "#000");
          this.storeChange("menuBackgroundCustom", "#fff");
          this.storeChange("menuColorActiveCustom", "#fff");
          this.storeChange("subMenuBackgroundCustom", obj.theme + "19");
          this.storeChange("subMenuBackgroundActiveCustom", obj.theme);
          this.storeChange("subMenuHoverCustom", obj.theme + "29");
        } else if (obj.sideTheme == "theme-dark") {
          this.storeChange("menuColorCustom", "#fff");
          this.storeChange("menuBackgroundCustom", "#1C2439");
          this.storeChange("menuColorActiveCustom", "#fff");
          this.storeChange("subMenuBackgroundCustom", "#000");
          this.storeChange("subMenuBackgroundActiveCustom", obj.theme);
          this.storeChange("subMenuHoverCustom", "rgba(0, 0, 0, 0.3)");
        }
      } else {
        this.storeChange("menuColorCustom", obj.menuColorCustom);
        this.storeChange("menuBackgroundCustom", obj.menuBackgroundCustom);
        this.storeChange("menuColorActiveCustom", obj.menuColorActiveCustom);
        this.storeChange(
          "subMenuBackgroundCustom",
          obj.subMenuBackgroundCustom,
        );
        this.storeChange(
          "subMenuBackgroundActiveCustom",
          obj.subMenuBackgroundActiveCustom,
        );
        this.storeChange("subMenuHoverCustom", obj.subMenuHoverCustom);
        this.storeChange("topBackgroundCustom", obj.topBackgroundCustom);
        this.storeChange("topSvgCustom", obj.topSvgCustom);
      }
    },
  },
};
