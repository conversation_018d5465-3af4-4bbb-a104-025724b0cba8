import i18n from "@/i18n";
const enums = {
  boolean: [
    { label: i18n.t("common.yes"), value: true },
    { label: i18n.t("common.no"), value: false },
  ],
  type: [
    {
      label: "类型1",
      value: "1",
    },
    {
      label: "类型2",
      value: "2",
    },
  ],
  dictStatusList: [
    { label: "初始化", value: "0", listClass: "info", loading: true },
    { label: "默认", value: "1", listClass: "primary" },
    { label: "成功", value: "2", listClass: "success", cssClass: "xxx" },
    { label: "禁用", value: "3", listClass: "danger" },
    { label: "告警", value: "4", listClass: "warning" },
  ],
  dictStatusList4: [
    { label: "初始化", value: "0", titleClass: "info", loading: true },
    { label: "默认", value: "1", titleClass: "primary" },
    { label: "成功", value: "2", titleClass: "success" },
    { label: "禁用", value: "3", titleClass: "danger" },
    { label: "告警", value: "4", titleClass: "warning" },
  ],
  dictStatusList8: [
    {
      label: "初始化",
      value: "0",
      listClass: "info",
      icon: "el-icon-refresh-right",
    },
    {
      label: "默认",
      value: "1",
      listClass: "primary",
      icon: "el-icon-success",
    },
    {
      label: "成功",
      value: "2",
      listClass: "success",
      icon: "el-icon-success",
    },
    {
      label: "禁用",
      value: "3",
      listClass: "danger",
      icon: "el-icon-error",
    },
    {
      label: "告警",
      value: "4",
      listClass: "warning",
      icon: "el-icon-warning",
    },
  ],
  dictStatusList2: [
    { name: "初始化", code: "0" },
    { name: "默认", code: "1" },
    { name: "成功", code: "2" },
    { name: "禁用", code: "3" },
    { name: "告警", code: "4" },
  ],
  templateType: [
    { label: "模板1", value: "1", component: "template1" },
    { label: "模板2", value: "2", component: "template2" },
    { label: "模板3", value: "3" },
  ],
  difyAppTypes: [
    { label: "聊天助手", value: "chat", icon: "icon-goutong" },
    { label: "Agent", value: "agent-chat", icon: "icon-agent" },
    { label: "文本生成", value: "completion", icon: "icon-xinxi" },
    { label: "Chatflow", value: "advanced-chat", icon: "icon-goutong" },
    { label: "工作流", value: "workflow", icon: "icon-gongzuoliu" },
  ],
};

export function getEnum(keys) {
  if (keys.length == 1) {
    return enums[keys[0]];
  } else {
    let obj = {};
    keys.forEach((key) => {
      obj[key] = enums[key] || [];
    });
    return obj;
  }
}

export function getEnumLabelByValue(
  list,
  value,
  labelKey = "label",
  valueKey = "value",
) {
  if (typeof list == "string") {
    return (
      enums[list].find((item) => item[valueKey] == value)?.[labelKey] || ""
    );
  }
  return list.find((item) => item[valueKey] == value)?.[labelKey] || "";
}

// 处理字典逻辑
import { getSysDicts } from "@/api/common";

function processDictData(result) {
  return Object.keys(result).reduce((acc, key) => {
    acc[key] = (result[key] || []).map((item) => ({
      ...item,
      label: item.name,
      value: item.code,
      ...(item.remark ? JSON.parse(item.remark) : {}),
    }));
    return acc;
  }, {});
}

export async function getSysDictData(data) {
  try {
    let res = await getSysDicts({ typeCodeList: data });
    let result = res.result || {};
    return { data: processDictData(result) };
  } catch (error) {
    return { data: {} };
  }
}
//获取统一web字典数据，统一赋值到dictData
export function getDictList() {
  return new Promise((resolve, reject) => {
    let typeCodeList = Object.keys(this.dictData);
    if (typeCodeList.length == 0) return;
    typeCodeList.forEach((item) => {
      this.dictData[item] = [];
    });
    getSysDictData(typeCodeList)
      .then((res) => {
        typeCodeList.forEach((item) => {
          this.dictData[item] = res.data[item] || [];
        });
        //获取字典内容赋值后回调
        resolve();
      })
      .catch(() => {
        reject();
      });
  });
}
