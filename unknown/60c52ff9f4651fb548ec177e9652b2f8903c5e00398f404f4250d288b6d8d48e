<template>
  <div class="profile">
    <el-dialog
      :title="$t('profile.changePassword')"
      :visible.sync="dialogVisible"
      width="700px"
      :before-close="onCloseClick"
      :close-on-click-modal="false"
      @open="onProfileOpen"
      append-to-body
    >
      <div class="dig-userInfo" v-loading="loading">
        <!-- <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane :label="$t('profile.baseInfo')" name="1">
            <el-descriptions class="mgt16" :column="1" border>
              <el-descriptions-item v-if="userInfo.userType == '1'">
                <template slot="label">
                  <i class="el-icon-user"></i>
                  {{ $t("userList.pName") }}
                </template>
                {{ userInfo.userName }}
              </el-descriptions-item>
              <el-descriptions-item v-else>
                <template slot="label">
                  <i class="el-icon-postcard"></i>
                  {{ $t("userList.dn") }}
                </template>
                {{ userInfo.userDn }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  <i class="el-icon-coordinate"></i>
                  {{ $t("userList.pRoleName") }}
                </template>
                {{ userInfo.roleNames }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  <i class="el-icon-mobile-phone"></i>
                  {{ $t("userList.pTel") }}
                </template>
                {{ userInfo.userTel ? userInfo.userTel : "-" }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  <i class="el-icon-message"></i>
                  {{ $t("userList.pFax") }}
                </template>
                {{ userInfo.userEmail ? userInfo.userEmail : "-" }}
              </el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>
          <el-tab-pane
            :label="$t('profile.changePassword')"
            name="2"
            v-if="userInfo.userType == 1"
          > -->
        <div class="modify-pwd-content">
          <el-form
            @submit.native.prevent
            :model="userForm"
            class="mgt16"
            label-position="right"
            :label-width="isZh() ? '80px' : '150px'"
            :rules="rules"
            ref="userFormRef"
          >
            <sw-form-item :label="$t('userList.oldPassword')" prop="oldPwd">
              <el-input
                v-model="userForm.oldPwd"
                type="password"
                :placeholder="$t('placeholder.place')"
                clearable
                maxlength="64"
              ></el-input>
            </sw-form-item>
            <sw-form-item :label="$t('userList.newPassword')" prop="newPwd">
              <el-input
                v-model="userForm.newPwd"
                type="password"
                :placeholder="$t('placeholder.place')"
                clearable
                maxlength="64"
              ></el-input>
            </sw-form-item>
            <sw-form-item
              :label="$t('userList.confirmPassword')"
              prop="confirmPwd"
            >
              <el-input
                v-model="userForm.confirmPwd"
                type="password"
                :placeholder="$t('placeholder.place')"
                clearable
                maxlength="64"
              ></el-input>
            </sw-form-item>
          </el-form>
        </div>
        <!-- </el-tab-pane>
          <el-tab-pane
            :label="$t('profile.changePassword')"
            name="4"
            v-if="userInfo.userType == 2"
          >
            <div class="dig-content">
              <el-form
                @submit.native.prevent
                :model="ukeyForm"
                class="mgt16"
                label-position="right"
                :label-width="isZh() ? '80px' : '150px'"
                :rules="uKeyRules"
                ref="ukeyFormRef"
              >
                <sw-form-item :label="$t('userList.oldPassword')" prop="oldPwd">
                  <el-input
                    v-model="ukeyForm.oldPwd"
                    type="password"
                    :placeholder="$t('placeholder.place')"
                    clearable
                    maxlength="256"
                  ></el-input>
                </sw-form-item>
                <sw-form-item :label="$t('userList.newPassword')" prop="newPwd">
                  <el-input
                    v-model="ukeyForm.newPwd"
                    type="password"
                    :placeholder="$t('placeholder.place')"
                    clearable
                    maxlength="256"
                  ></el-input>
                </sw-form-item>
                <sw-form-item
                  :label="$t('userList.confirmPassword')"
                  prop="confirmPwd"
                >
                  <el-input
                    v-model="ukeyForm.confirmPwd"
                    type="password"
                    :placeholder="$t('placeholder.place')"
                    clearable
                    maxlength="256"
                  ></el-input>
                </sw-form-item>
                <sw-form-item>
                  <el-button
                    class="cancel-btn oper-btn"
                    @click="onCloseClick"
                    >{{ $t("common.close") }}</el-button
                  >
                  <el-button
                    type="primary"
                    class="oper-btn"
                    @click="onSubmitClick"
                    >{{ $t("common.submit") }}</el-button
                  >
                </sw-form-item>
              </el-form>
            </div>
          </el-tab-pane>
        </el-tabs> -->
      </div>
      <!-- <span
        slot="footer"
        class="dialog-footer"
        v-if="activeName == '1' || activeName == '3'"
      >
        <el-button class="cancel-btn oper-btn" @click="onCloseClick">{{
          $t("common.close")
        }}</el-button>
      </span> -->
      <div slot="footer" class="dialog-footer">
        <el-button class="cancel-btn oper-btn" @click="onCloseClick">{{
          $t("common.close")
        }}</el-button>
        <el-button type="primary" class="oper-btn" @click="onSubmitClick">{{
          $t("common.submit")
        }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { validatePassword_old, validUkeyPassword } from "@/utils/validate";
import { changePassword, getInfo } from "@/api/user";

export default {
  name: "profile",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    let validatePwd = (rule, value, callback) => {
      if (validatePassword_old(value)) {
        return callback();
      }
      return callback(new Error(this.$t("tips.password")));
    };

    let validateNewPwd = (rule, value, callback) => {
      if (
        this.userInfo.userName == this.userForm.newPwd ||
        this.userInfo.userName ==
          this.userForm.newPwd.split("").reverse().join("")
      ) {
        return callback(new Error(this.$t("initLogin.userNameSamePwd")));
      }
      if (validatePassword_old(value)) {
        return callback();
      }
      return callback(new Error(this.$t("tips.password")));
    };
    let validateCPwd = (rule, value, callback) => {
      if (value == this.userForm.newPwd) {
        return callback();
      }
      return callback(new Error(this.$t("validate.confirmPwd")));
    };
    let validateUkeyPwd = (rule, value, callback) => {
      if (validUkeyPassword(value)) {
        return callback();
      }
      return callback(new Error(this.$t("tips.ukeyPassword")));
    };

    let validateNewUkeyPwd = (rule, value, callback) => {
      if (validUkeyPassword(value)) {
        return callback();
      }
      return callback(new Error(this.$t("tips.ukeyPassword")));
    };
    let validateUkeyCPwd = (rule, value, callback) => {
      if (value == this.ukeyForm.newPwd) {
        return callback();
      }
      return callback(new Error(this.$t("validate.confirmPwd")));
    };
    return {
      activeName: "2",
      userForm: {
        oldPwd: "",
        newPwd: "",
        confirmPwd: "",
      },
      ukeyForm: {
        oldPwd: "",
        newPwd: "",
        confirmPwd: "",
      },
      userInfo: {},
      loading: false,
      rules: {
        oldPwd: [
          {
            required: true,
            message: this.$t("placeholder.password"),
            trigger: "blur",
          },
          { validator: validatePwd, trigger: "blur" },
        ],
        newPwd: [
          {
            required: true,
            message: this.$t("placeholder.password"),
            trigger: "blur",
          },
          { validator: validateNewPwd, trigger: "blur" },
        ],
        confirmPwd: [
          {
            required: true,
            message: this.$t("placeholder.confirmPassword"),
            trigger: "blur",
          },
          { validator: validateCPwd, trigger: "blur" },
        ],
      },
      uKeyRules: {
        oldPwd: [
          {
            required: true,
            message: this.$t("placeholder.pin"),
            trigger: "blur",
          },
          { validator: validateUkeyPwd, trigger: "blur" },
        ],
        newPwd: [
          {
            required: true,
            message: this.$t("placeholder.pin"),
            trigger: "blur",
          },
          { validator: validateNewUkeyPwd, trigger: "blur" },
        ],
        confirmPwd: [
          {
            required: true,
            message: this.$t("placeholder.confirmPin"),
            trigger: "blur",
          },
          { validator: validateUkeyCPwd, trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    onProfileOpen() {
      this.loading = true;
      getInfo()
        .then((res) => {
          this.loading = false;
          this.userInfo = res.data;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    onCloseClick() {
      if (this.activeName == "2") {
        this.$refs.userFormRef.resetFields();
      }
      if (this.activeName == "4") {
        this.$refs.ukeyFormRef.resetFields();
      }
      this.activeName = "2";
      this.$emit("eventClose");
    },
    handleClick() {
      if (this.activeName == 1) {
        if (this.$refs.ukeyFormRef) {
          this.$refs.ukeyFormRef.resetFields();
        }
        if (this.$refs.userFormRef) {
          this.$refs.userFormRef.resetFields();
        }
      }
    },
    onSubmitClick() {
      if (this.activeName == "2") {
        this.$refs.userFormRef.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let params = {
                userId: this.userInfo.id,
                userPwd: this.$sm3(this.userForm.newPwd),
                oldUserPwd: this.$sm3(this.userForm.oldPwd),
              };
              changePassword(params)
                .then((res) => {
                  this.loading = false;
                  if (res.isSuccess) {
                    this.successMsg(this.$t("profile.successTip"));
                    // 退出登录
                    this.onCloseClick();
                    this.$emit("eventSucc");
                  }
                })
                .catch(() => {
                  this.loading = false;
                });
            } catch (err) {
              this.loading = false;
              throw err;
            }
          } else {
            return false;
          }
        });
      } else if (this.activeName == "4") {
        this.$refs.ukeyFormRef.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              const status = await this.$ukey.changeUkeyPin(
                this.ukeyForm.oldPwd,
                this.ukeyForm.newPwd,
              );
              if (status == 0) {
                this.loading = false;
                this.successMsg(this.$t("profile.successTip"));
                // 退出登录
                this.onCloseClick();
                this.$emit("eventSucc");
              } else {
                this.loading = false;
                this.errorMsg(this.$t("msg.operError"));
              }
            } catch (err) {
              this.loading = false;
              throw err;
            }
          } else {
            return false;
          }
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.modify-pwd-content {
}
</style>
<style lang="scss">
dig-userInfo {
  padding: 0 20px;
}
.oper-btn-box {
  text-align: center;
}
.el-descriptions-item__label {
  //   width: 200px;
}
</style>
