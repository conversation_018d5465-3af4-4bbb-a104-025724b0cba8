<template>
  <div class="register">
    <div class="register-content" v-loading="loading"></div>
    <!-- logo -->
    <div class="logo-box">
      <img
        :src="logo"
        alt=""
        v-imageError="require('@/assets/logo/transparent.svg')"
      />
    </div>
  </div>
</template>

<script>
import transparentLogo from "@/assets/logo/transparent.svg";
import { isFinishedInit } from "@/api/register";
import defaultLogo from "@/assets/logo/logo.svg";
import { mapGetters } from "vuex";

export default {
  name: "Register",
  data() {
    return {
      loading: false,
    };
  },
  computed: {
    ...mapGetters(["loginLogo"]),
    isSysImgEnd() {
      return this.$store.state.sys.isSysImgEnd;
    },
    logo() {
      return !this.isSysImgEnd
        ? transparentLogo
        : this.loginLogo
        ? this.loginLogo
        : defaultLogo;
    },
  },
  mounted() {
    window.addEventListener("message", this.messageEvt);
    this.getIsLogin();
  },
  beforeDestroy() {
    window.removeEventListener("message", this.messageEvt);
  },
  methods: {
    messageEvt(event) {
      const { type, initValue } = event.data;
      if (type === "initData") {
        this.initDataFun(initValue);
      }
    },
    getIsLogin() {
      this.loading = true;
      isFinishedInit()
        .then((res) => {
          if (res.data.finish == "Y") {
            this.loading = false;
            this.$router.push({
              path: "/login",
            });
          } else {
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style lang="scss">
@import "~@/assets/styles/register.scss";
.register {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: linear-gradient(
    199deg,
    #e4eeff 11%,
    #e4eeff 11%,
    #ffffff 81%,
    #ffffff 81%
  );
  position: relative;
}
.register-content {
  background: #ffffff;
  width: 1000px;
  height: 560px;
  border-radius: 4px;
  box-shadow: 0px 0px 100px 24px var(--color-primary29);
}
@media (max-width: 1500px) {
  .register-content {
    width: 767px;
  }
}
</style>
<style lang="scss" scoped>
.logo-box {
  position: absolute;
  top: 40px;
  left: 40px;
  width: 244px;
  height: 80px;
  img {
    width: 100%;
    height: 100%;
  }
}
@media (max-width: 1550px) {
  .logo-box {
    width: 140px;
  }
}
</style>
