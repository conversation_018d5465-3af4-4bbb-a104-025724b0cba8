<template>
  <div class="login-tab">
    <div
      class="tab-item"
      v-for="(item, index) in loginModeList"
      :key="index"
      :class="item.initStatus == 1 ? 'active' : ''"
      @click="onChangeTab(item)"
    >
      {{ isZh() ? item.authModeCnName : item.authModeEnName }}
    </div>
  </div>
</template>
<script>
export default {
  name: "login-tab",
  props: {
    loginModeList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {};
  },
  methods: {
    onChangeTab(item) {
      this.$emit("eventTabChange", item.id);
    },
  },
};
</script>
<style lang="scss" scoped>
.login-tab {
  width: 100%;
  height: 26px;
  border-bottom: 1px solid #bbbbbb;
  display: flex;
  .tab-item {
    cursor: pointer;
    margin-right: 23px;
    font-size: 16px;
    line-height: 16px;
    letter-spacing: 0em;
    color: #444444;
    padding-bottom: 8px;
  }
  .active {
    font-weight: bold;
    color: #222222;
    border-bottom: 2px solid var(--color-primary);
  }
}
</style>
