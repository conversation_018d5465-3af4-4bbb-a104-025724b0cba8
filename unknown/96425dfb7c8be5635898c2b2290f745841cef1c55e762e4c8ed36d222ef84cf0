.btn-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.el-form-item {
  margin-bottom: 24px;
  .el-form-item__label {
    line-height: 32px;

    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #212121;
  }
  .el-form-item__content {
    .el-input__inner {
      height: 32px;
      line-height: 32px;
    }
    .el-input__icon {
      line-height: 32px;
    }
  }
}
.register-btn {
  width: 120px;
  height: 36px;

  font-size: 16px;
  border-radius: 4px;
  padding: 0;
  line-height: 16px;
}

.register-back-btn {
  border-color: #bbbbbb;
  background-color: #ffffff;

  &:focus {
    border-color: #bbbbbb;
    background-color: #ffffff;
    color: #606266;
  }

  &:hover {
    background-color: var(--color-primary19);
    border-color: var(--color-primary);
  }

  &:active {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
    color: #fff;
  }
}

.change-auth-btn {
  height: 28px;
  padding: 0px 10px 0px 12px;

  font-size: 14px;
  margin-bottom: 24px;

  .switch-svg {
    width: 12px;
    height: 13px;
    vertical-align: -0.15em;
  }
}

.init-oper-btn {
  height: 28px;

  font-size: 14px;
  font-weight: normal;
  line-height: 14px;
  letter-spacing: 0em;
  padding: 0px 14px;
}

.init-edit-btn {
  color: var(--color-primary);

  &:focus {
    border-color: #dcdfe6;
    background-color: #ffffff;
    color: var(--color-primary);
  }

  &:hover {
    background-color: var(--color-primary19);
    border-color: var(--color-primary);
  }

  &:active {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
    color: #fff;
  }
}

.init-delete-btn {
  color: #ea4040;

  &:focus {
    background: #fff;
    border-color: #dcdfe6;
    color: #ea4040 !important;
  }

  &:hover {
    background-color: #fed4d4;
    border-color: #ea4040;
    color: #fff !important;
  }

  &:active {
    background-color: #ea4040;
    border-color: #ea4040;
    color: #fff !important;
  }
}

.init-auth-btn {
  background-color: #40b450;
  border-color: #40b450;
  color: #fff;

  &:focus {
    border-color: #40b450;
    color: #fff !important;
    background-color: #40b450;
  }

  &:hover {
    background-color: #d9f0dc;
    border-color: #40b450;
    color: #40b450 !important;
  }

  &:active {
    background-color: #40b450;
    border-color: #40b450;
    color: #fff !important;
  }
}

.add-select {
  // width: 120px;
  line-height: 28px;
  width: 100%;
  height: 28px;
  .el-input__inner {
    height: 28px;
    line-height: 28px;
  }
  .el-input__icon {
    line-height: 28px;
  }
}
.add-input {
  line-height: 28px;
  width: 85%;
  height: 28px;
  .el-input__inner {
    height: 28px;
    line-height: 28px;
  }
  .el-input__icon {
    line-height: 28px;
  }
}
.red-color {
  .el-input__inner {
    border-color: #f56c6c !important;
  }
}

.normal-color {
  .el-input__inner {
    border-color: #dcdfe6 !important;

    &:hover {
      outline: none !important;
      border-color: #c0c4cc !important;
    }
    &:active {
      outline: none !important;
      border-color: #409eff !important;
    }
    &:focus {
      outline: none !important;
      border-color: #409eff !important;
    }
  }
}

.w200 {
  width: 200px;
}

.el-input.is-disabled .el-input__inner {
  background-color: #f1f1f1;
  border-color: #bbbbbb;
}
