<template>
  <div class="choose-file-dig">
    <el-dialog
      :title="$t('themeConfig.chooseFile')"
      :visible.sync="dialogVisible"
      width="700px"
      :modal-append-to-body="true"
      :close-on-click-modal="false"
      :before-close="onCloseClick"
      @open="onOpenDig"
    >
      <div v-loading="addLoading">
        <!-- <div class="btn-box download-btn-box">
          <el-button
            class="cancel-btn oper-btn w360"
            plain
            type="primary"
            @click="onDownloadClick"
            >{{ $t("themeConfig.downloadFile") }}</el-button
          >
          <el-tooltip
            class="mgl5"
            effect="dark"
            :content="$t('tips.downloadFile')"
            placement="top-end"
          >
            <span class="el-icon-question"></span>
          </el-tooltip>
        </div> -->
        <div class="choose-btn-box">
          <el-upload
            ref="upload"
            action=""
            accept="application/zip"
            :limit="1"
            :multiple="false"
            :auto-upload="false"
            :show-file-list="true"
            :on-remove="onRemoveFile"
            :on-change="onChangeFile"
            :on-exceed="onExceedFile"
            :file-list="fileList"
          >
            <el-button type="primary" class="oper-btn w360">{{
              $t("themeConfig.chooseFile")
            }}</el-button>
            <el-tooltip
              class="mgl5"
              effect="dark"
              :content="$t('tips.chooseFile_zip')"
              placement="top-end"
            >
              <span class="el-icon-question"></span>
            </el-tooltip>
          </el-upload>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="cancel-btn oper-btn" @click="onCloseClick">{{
          $t("common.cancel")
        }}</el-button>
        <el-button type="primary" class="oper-btn" @click="onSubmitClick">{{
          $t("common.submit")
        }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import settingsObj from "@/utils/theme-Template.js";
import beautifier from "js-beautify";
export default {
  name: "choose-file-dig",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    themeAll: {
      type: Array,
      default: () => [],
    },
    addLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      settingsObj,
      fileList: [],
      fileObj: null,
    };
  },
  methods: {
    onOpenDig() {
      this.fileList = [];
    },
    // 下载模板文件
    onDownloadClick() {
      const codeStr = beautifier(JSON.stringify(this.settingsObj), {
        indent_size: 2,
        space_in_empty_paren: true,
      });
      const blob = new Blob([codeStr], { type: "text/plain;charset=utf-8" });
      this.$download.saveAs(blob, "template.json");
    },
    // 添加文件时校验文件类型和文件格式
    async onChangeFile(file, fileList) {
      let newFile = file.raw || file;
      let fileNames = newFile.name.split(".");
      let type = fileNames[fileNames.length - 1];
      if (!(type == "zip")) {
        this.errorMsg(this.$t("themeConfig.fileType_zip"));
        this.fileList = [];
        return false;
      }
      //   let flag = await this.checkFile(file);
      //   if (flag) {
      this.fileList = [newFile];
      //   } else {
      //     this.fileList = [];
      //     return false;
      //   }
    },

    // 删除文件
    onRemoveFile(file, fileList) {
      this.fileList = fileList;
    },
    // 超出文件数时给出弹窗提示
    async onExceedFile(files, fileList) {
      //   console.log(files[0]);
      let fileNames = files[0].name.split(".");
      let type = fileNames[fileNames.length - 1];
      if (type != "zip") {
        this.errorMsg(this.$t("themeConfig.fileType_zip"));
        return false;
      }
      //   let flag = await this.checkFile(files[0]);
      //   if (flag) {
      this.confirmMessage(this.$t("themeConfig.existFile"))
        .then((res) => {
          this.fileList = [];
          this.fileList.push(files[0]);
        })
        .catch(() => {});
      //   } else {
      //     return false;
      //   }
    },
    // 获取文件信息.json
    getFileObj(file) {
      return new Promise(function (resolve, reject) {
        let reader = new FileReader();
        if (typeof FileReader === "undefined") {
          this.errorMsg(this.$t("validate.fileReader"));
          return false;
        }
        reader.readAsText(file.raw || file);
        reader.onload = function (e) {
          const fileString = e.target.result;
          resolve(fileString);
        };
      });
    },
    // 校验文件格式.json
    async checkFile(file) {
      if (!file) {
        this.errorMsg(this.$t("themeConfig.checkFileTip"));
        return false;
      }
      let flag = true;
      await this.getFileObj(file).then((res) => {
        this.fileObj = JSON.parse(res);
      });
      if (this.fileObj && typeof this.fileObj == "object") {
        // 不能重名
        if (this.fileObj.themeNameZh && this.fileObj.themeNameEn) {
          let flag2 = this.themeAll.some(
            (obj) =>
              obj.themeNameZh == this.fileObj.themeNameZh ||
              obj.themeNameEn == this.fileObj.themeNameEn,
          );
          if (flag2) {
            this.errorMsg(this.$t("themeConfig.sameFileTip"));
            return false;
          }
        }
        // 检测属性是否和示例文件相同
        let keysOne = Object.keys(this.settingsObj);
        let keysTwoStr = Object.keys(this.fileObj).join(",");
        keysOne.forEach((key) => {
          if (!keysTwoStr.includes(key)) {
            // console.log(key, keysTwoStr);
            flag = false;
          }
        });
      } else {
        flag = false;
      }
      if (!flag) {
        this.errorMsg(this.$t("themeConfig.checkFileTip"));
        return false;
      } else {
        return true;
      }
    },
    onCloseClick() {
      this.fileList = [];
      this.$emit("eventClose");
    },
    async onSubmitClick() {
      if (this.fileList.length <= 0) {
        this.errorMsg(this.$t("themeConfig.emptyFileTip"));
        return;
      } else {
        let file = this.fileList[0];
        // .zip文件
        await this.$emit("addTheme", file);
        // .json文件
        // await this.getFileObj(file).then((res) => {
        //   this.fileObj = JSON.parse(res);
        //   this.$emit("addTheme", this.fileObj);
        //   this.onCloseClick();
        // });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.choose-file-dig {
  .choose-btn-box {
    text-align: center;
    padding: 10px 0;
  }
  .footer-btn-box {
    text-align: center;
    // padding: 20px 0;
    padding-top: 30px;
  }
}
</style>
<style lang="scss">
.choose-file-dig {
  .el-upload-list__item {
    background: var(--color-primary19);
    border: 1px dotted var(--color-primary19);
  }
  // .el-upload-list__item .el-icon-upload-success {
  //   color: var(--color-primary19);
  // }
}
</style>
