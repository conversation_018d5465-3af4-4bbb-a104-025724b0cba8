<template>
  <div style="height: 100%">
    <div class="login">
      <div class="login-content">
        <!-- 图片 -->
        <div class="login-left">
          <img
            v-if="loginLeftLogo"
            :src="loginLeftLogo"
            v-imageError="require('@/assets/logo/login-logo.png')"
          />
          <!-- 默认背景图上显示产品名称 -->
          <div v-if="showProductName" class="default-login-left-font">
            <!-- 产品名 -->
            <div class="loginLeftLogoTitle">
              {{ productInfo.portalTitle }}
            </div>
            <!-- <div class="loginLeftLogoSubTitle">
              {{ productInfo.productEnName }}
            </div> -->
          </div>
          <div v-if="!loginLeftLogo" class="logionLeftLogoBg">
            <!-- 产品名 -->
            <div class="loginLeftLogoTitle">{{ loginLeftLogoTitle }}</div>
            <div class="loginLeftLogoSubTitle">{{ loginLeftLogoSubTitle }}</div>
          </div>
        </div>
        <!-- 内容区 -->
        <div class="login-right">
          <div class="right-box">
            <!-- 可能有多种加载方式 动态路由+iframe嵌套-->
            <div class="content-title">{{ $t("login.title") }}</div>
            <login-tab
              :loginModeList="loginModeList"
              @eventTabChange="eventTabChange"
            />
            <div class="right-content">
              <component
                v-if="isComponent"
                :is="componentName"
                :ref="`${componentName}Ref`"
                :redirect="redirect"
              ></component>
              <iframe
                v-else
                ref="iframeRef"
                :src="componentName"
                frameborder="no"
                style="width: 100%; height: 100%"
                scrolling="auto"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- logo -->
      <!-- <div class="logo-box">
        <img
          :src="logo"
          alt=""
          v-imageError="require('@/assets/logo/transparent.svg')"
        />
      </div> -->
    </div>
  </div>
</template>

<script>
import transparentLogo from "@/assets/logo/transparent.svg";
import defaultLeftLogo from "@/assets/logo/login-logo.png";
import { isExternal } from "@/utils/util.js";
import loginTab from "./components/loginTab.vue";
import loginUser from "./components/loginUser.vue";
import loginKey from "./components/loginKey.vue";
import { getAuthModeData } from "@/api/common";
import { getPortalInfo } from "@/api/ai/portal";
import defaultLogo from "@/assets/logo/logo.svg";
import { mapGetters } from "vuex";
export default {
  name: "Login",
  components: {
    loginTab,
    loginUser,
    loginKey,
  },
  data() {
    return {
      redirect: undefined,
      loginModeList: [],
      initMode: "1",
      componentName: "",
      defaultLogoList: {},
      productInfo: {},
    };
  },
  computed: {
    ...mapGetters(["loginImg", "loginLogo"]),
    isSysImgEnd() {
      return this.$store.state.sys.isSysImgEnd;
    },
    isComponent() {
      return !isExternal(this.componentName);
    },
    theme() {
      return this.$store.state.settings.theme;
    },
    loginLeftLogo() {
      return this.loginImg ? this.loginImg : defaultLeftLogo;
    },
    // 是否在图片上方显示产品名
    showProductName() {
      return !this.loginImg;
    },
    logo() {
      return !this.isSysImgEnd
        ? transparentLogo
        : this.loginLogo
        ? this.loginLogo
        : defaultLogo;
    },
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },
  created() {},
  mounted() {
    // 在登录页判断是否注册，若注册，需要跳转到登录界面
    getPortalInfo({})
      .then((res) => {
        this.productInfo = res.data;
      })
      .catch(() => {});
    getAuthModeData()
      .then((res) => {
        this.loginModeList = res.data.authModeList;
        this.componentName = this.loginModeList.filter((item) => {
          return item.initStatus == 1;
        })[0].routePath;
      })
      .catch(() => {});
  },
  methods: {
    eventTabChange(value) {
      this.loginModeList.forEach((item) => {
        item.initStatus = "0";
        if (item.id == value) {
          item.initStatus = "1";
        }
      });
      let selectArr = this.loginModeList.filter((item) => {
        return item.id == value;
      });
      this.componentName = selectArr[0].routePath;
    },
    eventLoading(value) {
      this.loading = value;
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  position: relative;
  .login-content {
    width: 1100px;
    height: 560px;
    border-radius: 4px;
    box-shadow: 0px 0px 100px 24px var(--color-primary29);
    background: #fff;
    display: flex;
    .login-left {
      width: 371px;
      position: relative;
      img {
        width: 100%;
        height: 100%;
      }
      .default-login-left-font {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        color: #fff;
        padding: 46px 36px;
        font-weight: 600;
        .loginLeftLogoTitle {
          font-size: 30px;
          line-height: 30px;
        }
        .loginLeftLogoSubTitle {
          margin-top: 20px;
          font-size: 25px;
          line-height: 25px;
        }
      }
      .logionLeftLogoBg {
        width: 100%;
        height: 100%;
        background: var(--color-primary);
        color: #fff;
        padding: 50px 30px;
        font-weight: 900;
        .loginLeftLogoTitle {
          font-size: 34px;
          line-height: 34px;
        }
        .loginLeftLogoSubTitle {
          margin-top: 26px;
          font-size: 25px;
          line-height: 25px;
        }
      }
    }

    .login-right {
      padding: 52px 0px 41px 0px;
      flex: 1;
      text-align: center;
      .right-box {
        display: inline-block;
        text-align: left;
        .content-title {
          font-size: 36px;
          font-weight: bold;
          line-height: 36px;
          letter-spacing: 0em;
          color: #222222;
          margin-bottom: 52px;
          text-align: left;
        }
        .right-content {
          height: calc(100% - 148px);
          margin-top: 34px;
        }
      }
    }
  }
  .el-login-footer {
    height: 40px;
    line-height: 40px;
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: #fff;
    font-family: Arial;
    font-size: 12px;
    letter-spacing: 1px;
  }
  .logo-box {
    position: absolute;
    top: 40px;
    left: 40px;
    max-width: 300px;
    max-height: 80px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  @media screen and (min-width: 1400px) and (max-width: 1670px) {
    .login-content {
      width: 924px;
      .login-left {
        width: 300px;
      }
      .login-right {
        padding: 52px 0 41px 0;
      }
    }
    .logo-box {
      max-width: 240px;
      max-height: 60px;
    }
  }
  @media screen and (min-width: 1250px) and (max-width: 1400px) {
    .login-content {
      width: 824px;
      .login-left {
        width: 300px;
      }
      .login-right {
        padding: 52px 0 41px 0;
      }
    }
    .logo-box {
      max-width: 200px;
      max-height: 60px;
    }
  }
  @media screen and (min-width: 700px) and (max-width: 1250px) {
    .login-content {
      width: 700px;
      .login-left {
        width: 250px;
      }
    }
    .logo-box {
      max-width: 180px;
      max-height: 40px;
    }
  }
}
</style>
