.my-panel-container {
  .chart-container {
    position: relative;
    .chart-box {
      position: absolute;
      z-index: 99999999;
    }
  }

  .panel-container {
    overflow-y: auto;
  }
  .vue-grid-item {
    border-radius: 4px;
    .item-text-box {
      //   padding: 10px 15px;
      overflow: hidden;
    }
  }
  .card-box {
    margin-bottom: 15px;
  }
  .card-top {
    .small-title {
      height: 34px;
      display: flex;
      justify-content: space-between;
      white-space: nowrap;
      position: relative;
      padding: 16px 16px 0 16px;
      font-size: 14px;
      font-weight: 700;
      color: #222;
      &.title-pre-bar {
        position: relative;
        // &::before {
        //   content: "";
        //   position: absolute;
        //   top: 18px;
        //   display: inline-block;
        //   height: 16px;
        //   left: 16px;
        //   width: 4px;
        //   background-color: var(--color-primary);
        //   border-radius: 16px;
        // }
      }
      .more-btn {
        position: absolute;
        right: 24px;
        top: 10px;
        color: #444;
      }
      .jump-btn {
        padding: 0 15px 0 10px;
        height: 26px;
        border: none;
        [class^="el-icon-"] {
          margin-right: 8px;
        }
      }
      .data-num {
        color: #666;
        font-size: 14px;
        .data-num-item {
          display: inline-block;
          .num {
            font-size: 16px;
          }
          ~ .data-num-item {
            margin-left: 10px;
          }
        }
      }
      .card-pagination {
        color: #000;
        font-weight: 400;
        font-size: 14px;
        > span.disabled {
          color: #bfbfbf;
        }
        .card-pagination-btn {
          cursor: pointer;
          padding: 0 8px;
        }
        .card-pagination-page {
          color: #bfbfbf;
          .card-pagination-page-current {
            color: #000;
          }
        }
      }
    }
  }
  //   数字组件
  .basic-grid-numbers-tooltip.is-light {
    border: none !important;
    box-shadow: 1px 1px 2px 2px rgb(0, 0, 0, 0.1);
    //   箭头颜色
    .popper__arrow::after {
      border-bottom-color: #fff !important;
      border-top-color: #ffffff !important;
    }
    //   箭头边框
    .popper__arrow {
      border-top-color: #ddd !important;
      border-bottom-color: #ddd !important;
    }
  }
}
