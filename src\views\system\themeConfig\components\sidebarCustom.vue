<template>
  <div class="sidebar-custom-container">
    <div class="left-container" v-if="currentThemeClass == 'theme-custom'">
      <div class="drawer-item">
        <!-- 背景颜色-主菜单 -->
        <span>背景色-菜单</span>
        <color-settings
          :isAddClass="true"
          :showAlpha="true"
          style="float: right; height: 26px; margin: -3px 0px 0 0"
          @setColor="setColor"
          colorName="menuBackgroundCustom"
        ></color-settings>
      </div>
      <!-- 背景颜色-子菜单 -->
      <div class="drawer-item">
        <span>背景色-子菜单</span>
        <color-settings
          :isAddClass="true"
          :showAlpha="true"
          style="float: right; height: 26px; margin: -3px 0px 0 0"
          @setColor="setColor"
          colorName="subMenuBackgroundCustom"
        ></color-settings>
      </div>
      <!-- 背景颜色-菜单高亮 -->
      <div class="drawer-item">
        <span>背景色-菜单高亮</span>
        <color-settings
          :isAddClass="true"
          :showAlpha="true"
          style="float: right; height: 26px; margin: -3px 0px 0 0"
          @setColor="setColor"
          colorName="subMenuBackgroundActiveCustom"
        ></color-settings>
      </div>
      <!-- 背景颜色-鼠标悬停菜单阴影 -->
      <div class="drawer-item">
        <span>背景色-鼠标悬停菜单阴影</span>
        <color-settings
          :isAddClass="true"
          :showAlpha="true"
          style="float: right; height: 26px; margin: -3px 0px 0 0"
          @setColor="setColor"
          colorName="subMenuHoverCustom"
        ></color-settings>
      </div>
      <!-- 文字颜色 -->
      <div class="drawer-item">
        <span>文字颜色</span>
        <color-settings
          :isAddClass="true"
          :showAlpha="true"
          style="float: right; height: 26px; margin: -3px 0px 0 0"
          @setColor="setColor"
          colorName="menuColorCustom"
        ></color-settings>
      </div>
      <!-- 文字颜色-高亮 -->
      <div class="drawer-item">
        <span>文字颜色-高亮 </span>
        <color-settings
          :isAddClass="true"
          :showAlpha="true"
          style="float: right; height: 26px; margin: -3px 0px 0 0"
          @setColor="setColor"
          colorName="menuColorActiveCustom"
        ></color-settings>
      </div>
    </div>
    <div class="right-container" :class="settings.sideTheme">
      <div class="demo-tip">效果预览</div>
      <el-menu
        default-active="2-1"
        class="el-menu-demo"
        :collapse="false"
        :collapse-transition="false"
        mode="vertical"
        :unique-opened="true"
        :background-color="settings.menuBackgroundCustom"
        :text-color="settings.menuColorCustom"
      >
        <el-menu-item index="1" class="submenu-title-noDropdown">
          <i class="el-icon-collection icon"></i>
          <span slot="title">菜单1</span>
        </el-menu-item>

        <el-submenu index="2">
          <template slot="title">
            <i
              class="el-icon-collection icon"
              :style="{ color: settings.menuColorCustom }"
            ></i>
            <span>菜单2</span>
          </template>
          <el-menu-item index="2-1">子菜单1</el-menu-item>
          <el-menu-item index="2-2">子菜单2</el-menu-item>
          <el-menu-item index="2-3">子菜单3</el-menu-item>
          <el-menu-item index="2-4">子菜单4</el-menu-item>
        </el-submenu>
      </el-menu>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import ColorSettings from "./colorSettings.vue";
export default {
  name: "SidebarCustom",
  components: { ColorSettings },
  computed: {
    ...mapState(["settings"]),
  },
  props: {
    currentThemeClass: {
      type: String,
      default: "",
    },
    currentThemeObj: {
      type: Object,
      default: () => {},
    },
  },
  methods: {
    // 自定义主题
    setColor(colorName, color) {
      this.$emit("setColor", colorName, color);
    },
  },
};
</script>

<style lang="scss" scoped>
.sidebar-custom-container {
  display: flex;
  .left-container {
    height: 100%;
    font-size: 14px;
    line-height: 1.5;
    word-wrap: break-word;

    .drawer-item {
      width: 300px;
      color: rgba(0, 0, 0, 0.65);
      font-size: 14px;
      margin: 12px 0;
      height: 38px;
      line-height: 38px;
    }

    .drawer-switch {
      float: right;
    }
  }
  .right-container {
    margin-left: 40px;
  }
}
</style>
<style lang="scss"></style>
