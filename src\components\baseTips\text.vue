<template>
  <div class="tips-text">
    <template v-if="$slots.title">
      <slot name="title"></slot>
    </template>
    <template v-else>
      {{ title }}
    </template>
  </div>
</template>
<script>
export default {
  name: "textTips",
  props: {
    title: {
      type: String,
      default: "",
    },
  },
};
</script>
<style lang="scss" scoped>
.tips-text {
  line-height: 18px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
}
</style>
