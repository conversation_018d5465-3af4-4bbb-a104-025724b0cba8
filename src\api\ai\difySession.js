import request from "@/utils/request";
import config from "../configs";

let baseUrl = `${config.PROJAPI}/hub/app`;
export function getSessionList(data) {
  return request({
    url: `${baseUrl}/getConvserationList`,
    method: "post",
    data,
  });
}
export function editSession(data) {
  return request({
    url: `${baseUrl}/renameConversation`,
    method: "post",
    data,
  });
}
export function deleteSession(data) {
  return request({
    url: `${baseUrl}/deleteConversation`,
    method: "post",
    data,
  });
}
