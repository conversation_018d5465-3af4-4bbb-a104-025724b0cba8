{"common": {"LastUpdateTime": "Last upgrade time", "LastUpgradeStatus": "Last upgrade status", "Time": "Time", "abnormal": "abnormal", "activation": "Active", "add": "Add", "addAndSave": "Add And Save", "addAuth": "Authorize", "allow": "Allow", "approved": "Approved", "audited": "Audited", "authPassword": "Authenticate Password", "authTip": "Please set the operation permission label value", "autograph": "Sign", "backEndTip": "Back end interface connection exception", "backup": "Backup", "backupFail": "Backup Failed", "backuping": "Backup in Progress", "batchAdd": "<PERSON><PERSON> Add", "batchDelete": "<PERSON><PERSON> Delete", "batchDeleteAuth": "Batch deletion", "batchOper": "Batch Operations", "batchSign": "Batch Inspection", "batchStartService": "Batch start service", "batchStopService": "Batch stop service", "batchUnbind": "Batch unbinding", "batchUpgrade": "Batch Upgrade", "beforeWeek": "Last Week", "cancel": "Cancel", "checkResult": "Inspection Result ", "choosePicture": "Choose Picture", "clear": "Clear", "clickUpload": "Click Upload", "close": "Close", "code": "Code", "collectMode": "Collection method", "collectUrl": "Collection Address", "companyName": "Company Name", "config": "Config", "console": "<PERSON><PERSON><PERSON>", "contactAddress": "Contact Address", "contactPerson": "Contacts", "contactPhone": "Contact Phone", "content": "Content", "copy": "Copy", "copyPublicKey": "Copy Public Key", "createTime": "Creation time", "createUser": "Creator", "cropper": "C<PERSON>per", "cropperClick": "Image Cropping Area", "custom": "Custom", "default": "<PERSON><PERSON><PERSON>", "delete": "Delete", "deletePublicKey": "Clear Public Key", "desc": "Description", "description": "Description", "detail": "Detail", "detailInfo": "Information", "determine": "Submit", "disable": "Disabled", "dn": "Dn", "doEncrypt": "Encrypted", "downLoad": "DownLoad", "downLoading": "Downloading data, please wait", "downloadError": "Error downloading file, please contact the administrator!", "edit": "Edit", "email": "Email", "enable": "Enable", "encryptMode": "Encryption Mode", "encryptPassword": "Encrypted Password", "endTime": "End Time", "error": "Error", "errorItems": "Abnormal item", "errorReason": "Exception Reason", "errorUk": "USBKEY Error", "expireTime": "Expiration Time", "export": "Export", "fail": "Invalid session, or the session has expired, please log in again", "failReason": "Failure reason", "failed": "Failed", "findRule": "Matching Rules", "firstName": "First Name", "forgetPassword": "Forget Password", "functions": "Features", "ge": "piece", "generateAuthCode": "Generate Authorization Code", "getVerifyCode": "Get Code", "getVerifyCodeLater": "Refetch it after {countdown}s", "happenTime": "Occurrence Time", "history": "History", "historyReport": "History Report", "home": "Home", "hour": "Hour", "import": "Import", "importFail": "Import Failed", "importIng": "Importing in progress", "importLog": "Import Records", "importSuccess": "Import Successful", "importTime": "Import Time", "information": "Information", "initPerson": "Init Person", "interFace": "System Interface", "ip": "IP", "isBuiltIn": "Built-in", "lastActiveTime": "Last Active Time", "lastName": "Last Name", "learnMore": "Learn More", "levelMessage": "Message", "login": "<PERSON><PERSON>", "logout": "Logout", "logoutTip": "Are you sure to log off and exit the system?", "minute": "Minute", "more": "More", "moreTime": "Longer", "name": "Name", "no": "No", "noData": "No data", "none": "nothing", "normalItems": "Normal Items", "nosupport": "Not supported", "notAllow": "Not allowed", "off": "Off", "on": "On", "oneKeyCopy": "One-Key Coping", "onlySave": "Save", "operTime": "Operating Time", "operUser": "Operator", "operatingRecord": "Operating Record", "operation": "Operation", "os": "OS", "overview": "Overview", "pageFlowTitle": "Process Guidelines", "password": "Password", "pending": "Pending", "personal": "Personal Center", "phoneNo": "Phone Number", "pictureType": "Image type requirements：jpeg、jpg、png", "profile": "Profile", "reLogin": "Re-login", "reLoginTip": "Your login status has expired. please login again", "refresh": "Refresh", "reject": "Reject", "remark": "Remarks", "remoteConnect": "Remote connection", "removeOut": "Remove", "rename": "<PERSON><PERSON>", "reportName": "Report Name", "reportTime": "Report Time", "requestPending": "The data is being processed. Please do not submit it again", "reset": "Reset", "resetPwd": "Reset Password", "restart": "<PERSON><PERSON>", "restartTip": "The system is restarting, please wait...", "result": "Result", "roleSetting": "Permission settings", "save": "Save", "scene": "Application Scenarios", "search": "Search", "selectAll": "Select All", "selected": "Selected", "serverIp": "Server IP", "setNetworkCardUpdateTip": "The configuration has been updated, please try modifying the IP address", "setPublicKey": "Set Public Key", "shortName": "ShortName", "signin": "Sign In", "signup": "Sign Up", "signupHasAccount": "already have an account?", "signupSuccess": "Sign up successful", "sn": "Sn", "sort": "Sort", "specifications": "Specifications", "start": "Start", "startService": "Start Service", "startTime": "Start Time", "startUp": "Start-up", "status": "State", "stop": "Stop", "stopService": "Stop Service", "submit": "Submit", "submitSuccess": "Submitted successfully", "success": "Success", "synchro": "Sync Now", "sysInterTip": "System interface request timeout", "sysTip": "System Prompt", "thematicStyle": "Theme Style", "threshold": "<PERSON><PERSON><PERSON><PERSON>", "timeRange": "Time Range", "tips": "Tips", "today": "Today", "transferMode": "Transmission Mode", "type": "Type", "ukBtn": "Read USBKEY", "ukControl": "No USBKEY control found, please login again", "unInsetUk": "USBKEY not inserted, please login again", "unaudited": "Unaudited", "unbinding": "Unbinding", "unit": "Unit", "updateTime": "Update Time", "updateUk": "USBKEY has been replaced, please login again", "upgrade": "Upgrade", "upgradeStatus": "Upgrade Status", "uploadPicture": "Please upload pictures", "username": "User Name", "version": "Version", "versionLog": "Version Record", "versionNumber": "Version", "view": "View", "viewDetail": "View Details", "viewPersonInfo": "Personnel information", "visitProtocol": "Access Protocol", "visitTime": "Access Time", "warn": "<PERSON><PERSON>", "yes": "Yes", "yesterday": "Yesterday"}, "commonCertKey": {"authRequest": "Cert Request", "bindCert": "Bind Certificate", "blacklistData": "SecretData", "caName": "CA Name", "cert": "Cert", "certCN": "Certificate CN", "certFile": "Cert File", "certInfo": "Cert Information", "certName": "Cert Name", "certPwd": "Cert Password", "certValidDate": "Cert Validity", "commonName": "Common Name", "countryName": "Country Name", "createP10": "Create P10", "curveName": "Curve Name", "decKeyName": "decryption key ", "digestValue": "Digest Value", "encryCert": "Encryption certificate", "encryptCert": "Encryption Certificate", "importEnvelopeKey": "Envelope Key Import", "importPriKey": "Import Private Key", "importPubKey": "Import Public Key", "importPubPriKey": "Import public and private keys", "installCert": "Install Cert", "issuingAuth": "Issuing Authority", "issuingAuthCert": "Issuing Authority Cert", "keyLength": "Key Length", "keyMaterial": "Key Material", "keyName": "Key Name", "keyPurpose": "Key Purpose", "keyStatus": "Key State", "keyType": "Key Type", "manualRenovate": "Manual Rotation", "organization": "Organization Unit Name", "organizationName": "Organization Name", "privateKey": "Private<PERSON><PERSON>", "privateKeyMaterical": "Private Key Material", "publicKey": "PublicKey", "publicKeyMaterical": "Public Key Material", "publisher": "Publisher", "regionAndProvinceName": "Province", "regionName": "Region Name", "secretData": "SecretData", "signatureRequest": "Issue certificates", "subjection": "Subjection", "symmetricKey": "SymmetricKey", "symmetricKeyType": "Symmetric Key Type", "user": "User", "weight1": "Weight 1", "weight2": "Weight 2"}, "commonNet": {"dns": "DNS", "electricPort": "Electric Port", "enableSSL": "Enable SSL", "gateway": "Gateway", "ip": "IP", "ipAddress": "IP Adress", "ipPort": "IP Port", "ipv4": "IPv4", "ipv4Address": "IPv4 Address", "ipv4Gateway": "IPv4 Gateway", "ipv6": "IPv6", "ipv6Gateway": "IPv6 Gateway", "macAddress": "MAC Adress", "mask": "Mask", "mgtPort": "Management Port", "netStatus": "Network Port Status", "param": "Request Parameters", "ping": "PING", "port": "Port", "protocol": "Protocol ", "protocolConfig": "Protocol Configuration", "protocolType": "Protocol Type", "requestIp": "Request IP", "requestPort": "Request Port", "requestTime": "Request Time", "subnetMask": "Subnet Mask", "topology": "Topology", "url": "URL", "useIpv6": "Enable IPv6", "virtualIp": "Virtual IP"}, "error": {"back": "Go back", "goback": "Back to home page", "pageError1": "401 error!", "pageError4": "404 error!", "tip4": "Sorry, the page you are looking for does not exist. Try checking the URL for errors, then press the refresh button on the browser or try to find something else in our application.", "tipTitle1": "You do not have access!", "tipTitle4": "Page not found!", "tips1": "Sorry, you do not have access rights, please do not carry out illegal operations! You can return to the main page"}, "freeUse": {}, "login": {"logout": "Log Out", "title": "Welcome to login", "verificationCode": "Verification code"}, "msg": {"add": "Added successfully", "audit": "Audit successful", "auth": "Authorization successful", "bind": "Binding Successful", "bound": "Binding successful", "change": "Switching successful", "checkFailed": "Validation Failure", "checkSuccess": "Verification successful", "clear": "Clear successfully", "config": "Config successfully", "copy": "Copy successfully", "copyFail": "<PERSON><PERSON> Failed", "delete": "Deleted successfully", "downLoadFail": "Download Failed", "edit": "Edit successfully", "exportSuccess": "Export successful", "exportTips": "Are you sure you want to export ?", "import": "Import successfully", "install": "Install successfully", "login": "Login successful", "modify": "Modified successfully", "oper": "<PERSON><PERSON> successfully", "operError": "Operation failed", "removeOutSuccess": "Removal successful", "resetPwd": "Password Reset successfully", "rollback": "<PERSON><PERSON> successfully", "save": "Save successfully", "sendSucc": "Sent successfully, please check receipt", "set": "Set successfully", "start": "Start Successfully", "stop": "Stop successfully", "sync": "<PERSON><PERSON> successfully", "ukPinErrorTip": "Password verification failed, please check if the password is entered correctly", "unbind": "Unbind successfully", "unbinding": "Unbind successfully", "update": "Update successfully", "upgrade": "Upgrade successfully", "upload": "Upload Successfully"}, "placeholder": {"captcha": "Please enter captcha", "companyName": "Please enter company name", "email": "Please enter email", "emailCode": "Please enter the email verification code", "firstName": "Please enter first name", "ip": "Please enter ip", "ipv4": "Please enter IPv4", "ipv6": "Please enter IPv6", "lastName": "Please enter last name", "netMask": "Please enter the subnet mask", "password": "Please enter password", "passwordAgain": "Please enter password again", "path": "Please enter the path", "phone": "Please enter phone", "phoneNo": "Please enter your phone number", "pin": "Please enter USBKEY pin", "place": "Please enter", "port": "Please enter port", "pwd": "Please input a password", "readKey": "Please read key", "remark": "Please enter remark", "require": "Please enter the required fields", "select": "Please select", "sslAlgorithms": "Please select the algorithms", "tellYourNeeds": "Tell us more about your needs", "uploadFile": "Please upload the file", "username": "Please enter name", "value": "Please enter value", "verificationCode": "Please enter verification code"}, "product": {"specifications": "Specifications"}, "profile": {"LoginDateDueChangePwd": "The user password has not been changed for a long time. Please change the password", "about": "About", "baseInfo": "Essential Information", "changePassword": "Change Password", "componentName": "Component Name", "componentVersion": "Component version", "firstLoginChangePwd": "The password must be changed upon the first login", "reLoginTip": "You have changed your password, please log in again", "successTip": "Password changed successfully", "tenantInfo": "Tenant Information", "userInfo": "Personal Information"}, "tagsView": {"close": "Close Page", "closeAll": "Close All", "closeCurrent": "Close Current", "closeLeft": "Close Left", "closeOther": "Close Other", "closeRight": "Close Right", "refresh": "Refresh Page"}, "tips": {"checkMaxLen": "The maximum length limit is {max}.", "connectFail": "connection failed", "deleteTip": "After deletion, it will not be recoverable. Are you sure you want to delete it?", "password": "Supports length 8-64, must include at least three of the following: uppercase letters, lowercase letters, numbers, special characters ~!@#$%^&*{};,.?/'\\\"", "removeOutTip": "Are you sure you want to remove it?", "ukeyPassword": "The value contains 8 to 16 characters and can contain uppercase letters, lowercase letters, digits, and special characters ~! @ # $% ^ & * {}; ,.? / '\"", "username": "Supports length 3-15, can only contain uppercase and lowercase letters, numbers, special characters _-"}, "userList": {"add": "Add User", "addUser": "Add User Info", "authMode": "Authentication Mode", "certificate": "Certificate", "confirmPassword": "Confirm Password", "createCert": "Create cert", "deleteBatchTip": "Are you sure you want to delete the selected user?", "deleteTip": "Are you sure you want to delete this user?", "dn": "Dn", "downloadCert": "Download cert", "edit": "Edit User", "editUser": "Edit User Info", "integrVerification": "Integrity Verification", "lastLoginTime": "Last Login Time", "newPassword": "New Password", "oldPassword": "Old Password", "pCountry": "<PERSON>ress", "pCreatetime": "Create Time", "pFax": "Email", "pName": "User Name", "pRemarks": "Remark", "pRole": "Role", "pRoleName": "Role Name", "pTel": "Phone", "resetPaw": "Reset Password", "title": "User List", "updateCert": "Update cert"}, "validate": {"XssLen": "Not allowed to input<>special characters with a length of {0}- {1} Position", "checkUKeyType": "Please check if the USBKEY type matches", "confirmPwd": "The passwords entered twice are inconsistent", "email": "Please enter the corrent email", "floatRange": "Enter a number between {min} and {max}", "insertUk": "USBKEY not inserted detected", "installUk": "Please install USBKEY components first", "ip": "Please enter the correct IP", "ipv4": "Please enter the correct IPv4 format", "ipv6": "Please enter the correct IPv6 Gateway format", "mask": "Please enter the correct subnet mask", "num": "Please enter an integer", "password": "Contain at least uppercase letters, lowercase letters, numbers, and special characters~!@#$%^&*{}; ,.? /'\\\" with length {min}-{max}", "path": "Please enter the correct path", "phone": "Please enter the correct phone", "port": "The port format is incorrect", "portRange": "The range of ports is from {min} to {max}", "uKeyPasswordError": "The password is incorrect, please re-enter", "uSBKeyCertEmpty": "There is no certificate in the USBKEY, please replace the device before proceeding"}}