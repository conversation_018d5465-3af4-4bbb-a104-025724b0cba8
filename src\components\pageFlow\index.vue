<template>
  <div class="flow-content" style="display: flex">
    <div class="flow-item" v-for="(flow, idx) in flows" :key="idx">
      <div class="flow-item-title">
        <span>{{ flow.title }}</span>
      </div>
      <div class="flow-item-desc">
        <span>{{ flow.content }}</span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "pageHeader",
  props: {
    title: {
      type: String,
      default: "",
    },
    flows: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      showFlow: false,
    };
  },
};
</script>
<style lang="scss" scoped>
.flow-content {
  display: flex;
  .flow-item {
    margin-right: 24px;
    min-width: 200px;
    position: relative;
    flex: 1;
    &::after {
      content: "→";
      position: absolute;
      right: -22px;
      font-size: 18px;
      top: 0px;
    }
    &:last-child {
      margin-right: 0px;
      &::after {
        content: "";
      }
    }
    .flow-item-title {
      background: #f5f5f5;
      border: 1px solid #d0d2d4;
      border-radius: 4px;
      padding: 0px 10px;
      margin-bottom: 10px;
      text-align: center;
    }
    .flow-item-desc {
      border: 1px solid #d0d2d4;
      border-radius: 4px;
      padding: 10px;
      font-size: 12px;
    }
  }
}
</style>
