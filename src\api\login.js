import request from "@/utils/request";
import config from "./configs";

let baseUrl = `${config.PROJAPI}/user`;

// 登录方法用户名/密码
export function loginUser(data) {
  return request({
    url: `${baseUrl}/login`,
    headers: {
      isToken: false,
    },
    method: "post",
    data,
  });
}

// 登录 uk
export function loginUk(data) {
  return request({
    url: "/login/loginByUk",
    headers: {
      isToken: false,
    },
    method: "post",
    data,
  });
}

// 退出方法
export function logout() {
  return request({
    url: `${baseUrl}/logout`,
    method: "post",
    headers: {
      repeatSubmit: false,
    },
  });
}

// 获取验证码
export function getCodeImg(id) {
  return request({
    url: `${baseUrl}/captcha/image/generate`,
    headers: {
      isToken: false,
    },
    method: "post",
    timeout: 20000,
  });
}
// 生成随机数
export function getSecretKey() {
  return request({
    url: "/login/getSecretKey",
    method: "get",
  });
}
// 校验uk
export function checkUk(data) {
  return request({
    url: "/login/ukCheck",
    method: "post",
    data,
  });
}

// 判断是否显示验证码
export function getUserStatus(params) {
  return request({
    url: `${baseUrl}/getUserStatus`,
    method: "get",
    params,
  });
}
